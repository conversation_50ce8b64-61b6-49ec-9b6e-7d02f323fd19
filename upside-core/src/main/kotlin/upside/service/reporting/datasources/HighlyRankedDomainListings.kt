package upside.service.reporting.datasources

import nct.core.settings.business.FetchMarketListingDataSource
import org.springframework.stereotype.Component
import upside.service.etl.EtlTable
import upside.service.etl.model.AddressEtl
import upside.service.etl.model.AgencyEtl
import upside.service.etl.model.AgentEtl
import upside.service.etl.model.InteractionEtl
import upside.service.etl.model.PropertyEtl
import upside.service.etl.model.SparkEtl
import upside.service.etl.model.portals.DomainListingEtl
import upside.service.etl.model.view.SparkViews
import upside.service.reporting.queries.Utils
import upside.service.reporting.queries.Utils.agent
import upside.service.reporting.queries.Utils.appraisal
import upside.service.reporting.queries.Utils.date
import upside.service.reporting.queries.Utils.funnelStatus
import upside.service.reporting.queries.Utils.interaction
import upside.service.reporting.queries.Utils.property
import upside.service.reporting.queries.Utils.spark
import upside.service.reporting.queries.Utils.sqlFilter
import upside.service.reporting.queries.Utils.teamAgentJoinLine
import upside.service.reporting.queries.Utils.teamAgentTableLine
import upside.settings.model.MarketListingDataSource
import upside.types.interaction.InteractionType
import upside.types.organisation.Organisation
import upside.types.property.classifications.PropertyPhase
import upside.types.property.classifications.PropertyPhase.Campaign
import upside.types.property.classifications.PropertyPhase.CampaignPrep
import upside.types.property.classifications.PropertyPhase.Complete
import upside.types.property.classifications.PropertyPhase.Settlement
import upside.types.property.classifications.PropertyPhase.Withdrawn
import upside.types.reporting.BqReportingDataSource
import upside.types.reporting.ReportingFilter
import upside.types.reporting.ReportingQueryWindows

@Component
class HighlyRankedDomainListings(
    private val fetchMarketListingDataSource: FetchMarketListingDataSource
) : BqReportingDataSource {

    override fun querySqlString(
        org: Organisation,
        bigQueryProjectId: String,
        reportingFilter: ReportingFilter,
        windows: ReportingQueryWindows
    ): String {
        val marketListingDataSource = fetchMarketListingDataSource.fetch(org, reportingFilter)

        return when (marketListingDataSource) {
            MarketListingDataSource.MarketListing -> generateMarketListingQuery(org, bigQueryProjectId, reportingFilter, windows)
            MarketListingDataSource.DomainListing -> generateDomainListingQuery(org, bigQueryProjectId, reportingFilter, windows)
        }
    }

    private fun generateDomainListingQuery(
        org: Organisation,
        bigQueryProjectId: String,
        reportingFilter: ReportingFilter,
        windows: ReportingQueryWindows
    ): String {
        val start = windows.previousWindow?.start ?: windows.primaryWindow.start
        val end = windows.primaryWindow.end
        val timezoneId = windows.timezoneId
        val agencyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agency)
        val agentTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agent)
        val domainListingTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.DomainListing)
        val interactionTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.InteractionByInitiatedDateAndBusiness)
        val propertyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Property)
        val appraisalTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Appraisal)
        val sparkTable = Utils.fullTableName(org, bigQueryProjectId, SparkViews.HighScoreSparks.viewName)
        val propertyMatchableAddressField = "${PropertyEtl::address.name}.${AddressEtl::matchableAddress.name}"
        val initiatedUtcTimestampField = InteractionEtl::initiatedUtcTimestamp.name
        val domainListing = "domainListing"
        val domainListingDateListed = "$domainListing.${DomainListingEtl::dateListed.name}"
        val wonPhases: List<PropertyPhase> = listOf(CampaignPrep, Campaign, Withdrawn, Settlement, Complete)
        val wonPhasesString = wonPhases.joinToString(
            separator = ", ",
            prefix = "(",
            postfix = ")"
        ) { "'${it.name}'" }
        val agency = "agency"
        return """
            SELECT
            DATE($domainListingDateListed, '$timezoneId') as $date,
            ($property.${PropertyEtl::phase.name} IN $wonPhasesString OR $agency.${AgencyEtl::name.name} = COALESCE($domainListing.${DomainListingEtl::advertiserName.name}, 'Unknown')) AS $won,
            CASE
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.${InteractionEtl::type.name} = '${InteractionType.Call.name}' AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($domainListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($domainListingDateListed, '$timezoneId'))) THEN 'Not contacted'
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.callDetails.outcome = "Spoke" AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($domainListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($domainListingDateListed, '$timezoneId'))) THEN 'Attempted call, not connected'
                WHEN NOT EXISTS(SELECT * FROM $appraisalTable AS $appraisal WHERE $appraisal.propertyId = $property.id AND (DATE($appraisal.date, '$timezoneId') BETWEEN DATE_SUB(DATE($domainListingDateListed, '$timezoneId'), INTERVAL 12 MONTH) AND DATE($domainListingDateListed, '$timezoneId'))) THEN 'Connected, not appraised'
                ELSE 'Appraised'
            END AS $funnelStatus,
            FROM
            ${reportingFilter.teamAgentTableLine(org, bigQueryProjectId) ?: ""}
            `$agentTable` AS $agent,
            `$agencyTable` AS $agency,
            `$propertyTable` AS $property
            JOIN `$domainListingTable` AS $domainListing
                ON REPLACE(REPLACE($property.$propertyMatchableAddressField, ' ', ''), ',', '') = REPLACE(REPLACE($domainListing.${DomainListingEtl::matchableAddress.name}, ' ', ''), ',', '')
                AND $property.${PropertyEtl::created.name} < $domainListingDateListed
            WHERE $agent.${AgentEtl::id.name} = $property.${PropertyEtl::agent.name}
            AND $agent.${AgentEtl::business.name} = $property.${PropertyEtl::business.name}
            AND EXISTS(
                SELECT propertyId, created FROM $sparkTable AS $spark
                WHERE $property.${PropertyEtl::id.name} = $spark.${SparkEtl::propertyId.name}
                AND DATE($spark.${SparkEtl::created.name}, '$timezoneId') BETWEEN DATE_SUB(DATE($domainListingDateListed, '$timezoneId'), INTERVAL 90 DAY) AND DATE($domainListingDateListed, '$timezoneId')
                AND $spark.${SparkEtl::created.name} >= DATE_SUB(${Utils.timestampMillis(start.millis)}, INTERVAL 90 DAY)
                AND $spark.${SparkEtl::created.name} < ${Utils.timestampMillis(end.millis)}
            )
            ${reportingFilter.teamAgentJoinLine() ?: ""}
            AND $agency.${AgencyEtl::id.name} = $property.${PropertyEtl::agency.name}
            AND $domainListingDateListed >= ${Utils.timestampMillis(start.millis)}
            AND $domainListingDateListed < ${Utils.timestampMillis(end.millis)}
            AND listingType IN ('Sale', 'Sold')
            AND (($property.${PropertyEtl::phase.name} IN $wonPhasesString) OR ($property.${PropertyEtl::database.name}) OR ($property.${PropertyEtl::phase.name} = 'LostAndSold'))
            ${reportingFilter.sqlFilter()}
            ORDER BY $date DESC
        """.trimIndent()
    }

    private fun generateMarketListingQuery(
        org: Organisation,
        bigQueryProjectId: String,
        reportingFilter: ReportingFilter,
        windows: ReportingQueryWindows
    ): String {
        val start = windows.previousWindow?.start ?: windows.primaryWindow.start
        val end = windows.primaryWindow.end
        val timezoneId = windows.timezoneId
        val agencyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agency)
        val agentTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agent)
        val marketListingTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.MarketListing)
        val interactionTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.InteractionByInitiatedDateAndBusiness)
        val propertyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Property)
        val appraisalTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Appraisal)
        val sparkTable = Utils.fullTableName(org, bigQueryProjectId, SparkViews.HighScoreSparks.viewName)
        val propertyMatchableAddressField = "${PropertyEtl::address.name}.${AddressEtl::matchableAddress.name}"
        val initiatedUtcTimestampField = InteractionEtl::initiatedUtcTimestamp.name
        val marketListing = "marketListing"
        val marketListingDateListed = "$marketListing.list_date"
        val wonPhases: List<PropertyPhase> = listOf(CampaignPrep, Campaign, Withdrawn, Settlement, Complete)
        val wonPhasesString = wonPhases.joinToString(
            separator = ", ",
            prefix = "(",
            postfix = ")"
        ) { "'${it.name}'" }
        val agency = "agency"
        return """
            SELECT
            DATE($marketListingDateListed, '$timezoneId') as $date,
            ($property.${PropertyEtl::phase.name} IN $wonPhasesString OR $agency.${AgencyEtl::name.name} = COALESCE($marketListing.advertiser.name, 'Unknown')) AS $won,
            CASE
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.${InteractionEtl::type.name} = '${InteractionType.Call.name}' AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($marketListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($marketListingDateListed, '$timezoneId'))) THEN 'Not contacted'
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.callDetails.outcome = "Spoke" AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($marketListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($marketListingDateListed, '$timezoneId'))) THEN 'Attempted call, not connected'
                WHEN NOT EXISTS(SELECT * FROM $appraisalTable AS $appraisal WHERE $appraisal.propertyId = $property.id AND (DATE($appraisal.date, '$timezoneId') BETWEEN DATE_SUB(DATE($marketListingDateListed, '$timezoneId'), INTERVAL 12 MONTH) AND DATE($marketListingDateListed, '$timezoneId'))) THEN 'Connected, not appraised'
                ELSE 'Appraised'
            END AS $funnelStatus,
            FROM
            ${reportingFilter.teamAgentTableLine(org, bigQueryProjectId) ?: ""}
            `$agentTable` AS $agent,
            `$agencyTable` AS $agency,
            `$propertyTable` AS $property
            JOIN `$marketListingTable` AS $marketListing
                ON REPLACE(REPLACE($property.$propertyMatchableAddressField, ' ', ''), ',', '') = REPLACE(REPLACE(CONCAT(COALESCE($marketListing.address.unit_number, ''), ' ', COALESCE($marketListing.address.street_number, ''), ' ', COALESCE($marketListing.address.street_name, ''), ' ', COALESCE($marketListing.address.major_municipality, '')), ' ', ''), ',', '')
                AND $property.${PropertyEtl::created.name} < $marketListingDateListed
            WHERE $agent.${AgentEtl::id.name} = $property.${PropertyEtl::agent.name}
            AND $agent.${AgentEtl::business.name} = $property.${PropertyEtl::business.name}
            AND EXISTS(
                SELECT propertyId, created FROM $sparkTable AS $spark
                WHERE $property.${PropertyEtl::id.name} = $spark.${SparkEtl::propertyId.name}
                AND DATE($spark.${SparkEtl::created.name}, '$timezoneId') BETWEEN DATE_SUB(DATE($marketListingDateListed, '$timezoneId'), INTERVAL 90 DAY) AND DATE($marketListingDateListed, '$timezoneId')
                AND $spark.${SparkEtl::created.name} >= DATE_SUB(${Utils.timestampMillis(start.millis)}, INTERVAL 90 DAY)
                AND $spark.${SparkEtl::created.name} < ${Utils.timestampMillis(end.millis)}
            )
            ${reportingFilter.teamAgentJoinLine() ?: ""}
            AND $agency.${AgencyEtl::id.name} = $property.${PropertyEtl::agency.name}
            AND $marketListingDateListed >= ${Utils.timestampMillis(start.millis)}
            AND $marketListingDateListed < ${Utils.timestampMillis(end.millis)}
            AND $marketListing.listing_type IN ('Sale', 'Sold')
            AND (($property.${PropertyEtl::phase.name} IN $wonPhasesString) OR ($property.${PropertyEtl::database.name}) OR ($property.${PropertyEtl::phase.name} = 'LostAndSold'))
            ${reportingFilter.sqlFilter()}
            ORDER BY $date DESC
        """.trimIndent()
    }

    companion object {
        const val address = "address"
        const val propertyId = "property_id"
        const val listingSlug = "listing_slug"
        const val latitude = "latitude"
        const val longitude = "longitude"
        const val connected = "connected"
        const val leadScore = "lead_score"
        const val won = "won"
    }
}
