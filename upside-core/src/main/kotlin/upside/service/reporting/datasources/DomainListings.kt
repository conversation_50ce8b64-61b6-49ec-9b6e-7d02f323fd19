package upside.service.reporting.datasources

import nct.core.settings.business.FetchMarketListingDataSource
import nct.market.domain.adapter.datastore.ListingSource
import org.joda.time.DateTime
import org.springframework.stereotype.Component
import upside.model.property.appointments.AppointmentType
import upside.service.etl.EtlTable
import upside.service.etl.model.AddressEtl
import upside.service.etl.model.AgencyEtl
import upside.service.etl.model.AgentEtl
import upside.service.etl.model.InteractionEtl
import upside.service.etl.model.InteractionEtl.CallDetailsEtl
import upside.service.etl.model.PropertyEtl
import upside.service.etl.model.activity.LeadScoreActivityEtl
import upside.service.etl.model.portals.DomainListingEtl
import upside.service.reporting.queries.Utils
import upside.service.reporting.queries.Utils.address
import upside.service.reporting.queries.Utils.advertiserName
import upside.service.reporting.queries.Utils.agent
import upside.service.reporting.queries.Utils.agentAvatar
import upside.service.reporting.queries.Utils.agentName
import upside.service.reporting.queries.Utils.appraisal
import upside.service.reporting.queries.Utils.date
import upside.service.reporting.queries.Utils.funnelStatus
import upside.service.reporting.queries.Utils.inCurrentYear
import upside.service.reporting.queries.Utils.inLast7Days
import upside.service.reporting.queries.Utils.inPrevious
import upside.service.reporting.queries.Utils.interaction
import upside.service.reporting.queries.Utils.leadScore
import upside.service.reporting.queries.Utils.leadScoreActivity
import upside.service.reporting.queries.Utils.property
import upside.service.reporting.queries.Utils.sqlFilter
import upside.service.reporting.queries.Utils.teamAgentJoinLine
import upside.service.reporting.queries.Utils.teamAgentTableLine
import upside.settings.model.MarketListingDataSource
import upside.types.interaction.InteractionType
import upside.types.interaction.call.CallOutcome
import upside.types.organisation.Organisation
import upside.types.property.classifications.PropertyPhase
import upside.types.property.classifications.PropertyPhase.Campaign
import upside.types.property.classifications.PropertyPhase.CampaignPrep
import upside.types.property.classifications.PropertyPhase.Complete
import upside.types.property.classifications.PropertyPhase.Settlement
import upside.types.property.classifications.PropertyPhase.Withdrawn
import upside.types.reporting.BqReportingDataSource
import upside.types.reporting.ReportingFilter
import upside.types.reporting.ReportingQueryWindows

@Component
class DomainListings(
    private val fetchMarketListingDataSource: FetchMarketListingDataSource,
) : BqReportingDataSource {

    override fun querySqlString(
        org: Organisation,
        bigQueryProjectId: String,
        reportingFilter: ReportingFilter,
        windows: ReportingQueryWindows
    ): String {
        val marketListingDataSource = fetchMarketListingDataSource.fetch(org, reportingFilter)

        return when (marketListingDataSource) {
            MarketListingDataSource.MarketListing -> generateMarketListingQuery(org, bigQueryProjectId, reportingFilter, windows)
            MarketListingDataSource.DomainListing -> generateDomainListingQuery(org, bigQueryProjectId, reportingFilter, windows)
        }
    }

    private fun generateDomainListingQuery(
        org: Organisation,
        bigQueryProjectId: String,
        reportingFilter: ReportingFilter,
        windows: ReportingQueryWindows
    ): String {
        val start = windows.previousWindow?.start ?: windows.primaryWindow.start
        val end = windows.primaryWindow.end
        val timezoneId = windows.timezoneId
        val endOfDay = DateTime.now(start.zone).withTimeAtStartOfDay().plusDays(1)
        val agencyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agency)
        val agentTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agent)
        val domainListingTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.DomainListing)
        val interactionTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.InteractionByInitiatedDateAndBusiness)
        val leadScoreActivityTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.LeadScoreActivity)
        val propertyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Property)
        val appraisalTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Appraisal)
        val leadScoreActivityChangedField = LeadScoreActivityEtl::changed.name
        val propertyFormattedAddressField = "${PropertyEtl::address.name}.${AddressEtl::formattedAddress.name}"
        val propertyMatchableAddressField = "${PropertyEtl::address.name}.${AddressEtl::matchableAddress.name}"
        val callOutcomeField = "${InteractionEtl::callDetails.name}.${CallDetailsEtl::outcome.name}"
        val initiatedUtcTimestampField = InteractionEtl::initiatedUtcTimestamp.name
        val domainListing = "domainListing"
        val domainListingDateListed = "$domainListing.${DomainListingEtl::dateListed.name}"
        val agentAppraisalVisit = "$property.${PropertyEtl::appointments.name}.${AppointmentType.AGENT_APPRAISAL_VISIT.name}"
        val wonPhases: List<PropertyPhase> = listOf(CampaignPrep, Campaign, Withdrawn, Settlement, Complete)
        val wonPhasesString = wonPhases.joinToString(
            separator = ", ",
            prefix = "(",
            postfix = ")"
        ) { "'${it.name}'" }
        val agency = "agency"
        return """
            SELECT
            $agent.${AgentEtl::fullName.name} as $agentName,
            $agent.${AgentEtl::avatarUrl.name} as $agentAvatar,
            $property.$propertyFormattedAddressField as $address,
            $property.${PropertyEtl::id.name} as $propertyId,
            $domainListing.${DomainListingEtl::advertiserName.name} as $advertiserName,
            $domainListing.${DomainListingEtl::listingSlug.name} as $listingSlug,
            $domainListing.${DomainListingEtl::latitude.name} as $latitude,
            $domainListing.${DomainListingEtl::longitude.name} as $longitude,
            $domainListing.${DomainListingEtl::portalUrl.name} as $portalUrl,
            $domainListing.${DomainListingEtl::listingSource.name} as $listingSource,
            $domainListing.${DomainListingEtl::soldDate.name} as $soldDate,
            DATE($agentAppraisalVisit, '$timezoneId') as $agentAppraisalDate,
            $leadScoreActivity.${LeadScoreActivityEtl::leadScore.name} as $leadScore,
            DATE($interaction.$initiatedUtcTimestampField, '$timezoneId') AS $connected,
            ($property.${PropertyEtl::phase.name} IN $wonPhasesString OR $agency.${AgencyEtl::name.name} = COALESCE($domainListing.${DomainListingEtl::advertiserName.name}, 'Unknown')) AS $won,
            DATE($domainListingDateListed, '$timezoneId') as $date,
            TIMESTAMP_ADD($domainListingDateListed, INTERVAL 7 DAY) > ${Utils.timestampMillis(endOfDay.millis)} as $inLast7Days,
            EXTRACT(YEAR FROM $domainListingDateListed AT TIME ZONE '$timezoneId') = EXTRACT(YEAR FROM CURRENT_TIMESTAMP() AT TIME ZONE '$timezoneId') as $inCurrentYear,
            $domainListingDateListed < ${Utils.timestampMillis(windows.primaryWindow.start.millis)} as $inPrevious,
            CASE
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.${InteractionEtl::type.name} = '${InteractionType.Call.name}' AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($domainListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($domainListingDateListed, '$timezoneId'))) THEN 'Not contacted'
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.callDetails.outcome = "Spoke" AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($domainListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($domainListingDateListed, '$timezoneId'))) THEN 'Attempted call, not connected'
                WHEN NOT EXISTS(SELECT * FROM $appraisalTable AS $appraisal WHERE $appraisal.propertyId = $property.id AND (DATE($appraisal.date, '$timezoneId') BETWEEN DATE_SUB(DATE($domainListingDateListed, '$timezoneId'), INTERVAL 12 MONTH) AND DATE($domainListingDateListed, '$timezoneId'))) THEN 'Connected, not appraised'
                ELSE 'Appraised'
            END AS $funnelStatus,
            FROM
            ${reportingFilter.teamAgentTableLine(org, bigQueryProjectId) ?: ""}
            `$agentTable` AS $agent,
            `$agencyTable` AS $agency,
            `$propertyTable` AS $property
            JOIN `$domainListingTable` AS $domainListing
                ON REPLACE(REPLACE($property.$propertyMatchableAddressField, ' ', ''), ',', '') = REPLACE(REPLACE($domainListing.${DomainListingEtl::matchableAddress.name}, ' ', ''), ',', '')
                AND $property.${PropertyEtl::created.name} < $domainListingDateListed
            LEFT JOIN `$interactionTable` AS $interaction
                ON $interaction.${InteractionEtl::propertyId.name} = $property.${PropertyEtl::id.name}
                AND $interaction.$initiatedUtcTimestampField < $domainListingDateListed
                AND $interaction.$callOutcomeField = '${CallOutcome.Spoke.name}'
            LEFT JOIN `$interactionTable` AS ${interaction}2
                ON $interaction.${InteractionEtl::propertyId.name} = ${interaction}2.${InteractionEtl::propertyId.name}
                AND $interaction.$initiatedUtcTimestampField < ${interaction}2.$initiatedUtcTimestampField
                AND ${interaction}2.$initiatedUtcTimestampField < $domainListingDateListed
                AND ${interaction}2.$callOutcomeField = '${CallOutcome.Spoke.name}'
            LEFT JOIN `$leadScoreActivityTable` AS $leadScoreActivity
                ON $leadScoreActivity.${LeadScoreActivityEtl::property.name} = $property.${PropertyEtl::id.name}
                AND $leadScoreActivity.$leadScoreActivityChangedField < TIMESTAMP_SUB($domainListingDateListed, INTERVAL 1 DAY)
            LEFT JOIN `$leadScoreActivityTable` AS ${leadScoreActivity}2
                ON $leadScoreActivity.${LeadScoreActivityEtl::property.name} = ${leadScoreActivity}2.${LeadScoreActivityEtl::property.name}
                AND $leadScoreActivity.$leadScoreActivityChangedField < ${leadScoreActivity}2.$leadScoreActivityChangedField
                AND ${leadScoreActivity}2.$leadScoreActivityChangedField < TIMESTAMP_SUB($domainListingDateListed, INTERVAL 1 DAY)
            WHERE $agent.${AgentEtl::id.name} = $property.${PropertyEtl::agent.name}
            AND $agent.${AgentEtl::business.name} = $property.${PropertyEtl::business.name}
            ${reportingFilter.teamAgentJoinLine() ?: ""}
            AND ${interaction}2.${InteractionEtl::propertyId.name} IS NULL
            AND ${leadScoreActivity}2.${LeadScoreActivityEtl::property.name} IS NULL
            AND $agency.${AgencyEtl::id.name} = $property.${PropertyEtl::agency.name}
            AND $domainListingDateListed >= ${Utils.timestampMillis(start.millis)}
            AND $domainListingDateListed < ${Utils.timestampMillis(end.millis)}
            AND listingType IN ('Sale', 'Sold')
            AND (($property.${PropertyEtl::phase.name} IN $wonPhasesString) OR ($property.${PropertyEtl::database.name}) OR ($property.${PropertyEtl::phase.name} = 'LostAndSold'))
            ${reportingFilter.sqlFilter()}
            ORDER BY $date DESC
        """.trimIndent()
    }

    private fun generateMarketListingQuery(
        org: Organisation,
        bigQueryProjectId: String,
        reportingFilter: ReportingFilter,
        windows: ReportingQueryWindows
    ): String {
        val start = windows.previousWindow?.start ?: windows.primaryWindow.start
        val end = windows.primaryWindow.end
        val timezoneId = windows.timezoneId
        val endOfDay = DateTime.now(start.zone).withTimeAtStartOfDay().plusDays(1)
        val agencyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agency)
        val agentTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Agent)
        val marketListingTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.MarketListing)
        val interactionTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.InteractionByInitiatedDateAndBusiness)
        val leadScoreActivityTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.LeadScoreActivity)
        val propertyTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Property)
        val appraisalTable = Utils.fullTableName(org, bigQueryProjectId, EtlTable.Appraisal)
        val leadScoreActivityChangedField = LeadScoreActivityEtl::changed.name
        val propertyFormattedAddressField = "${PropertyEtl::address.name}.${AddressEtl::formattedAddress.name}"
        val propertyMatchableAddressField = "${PropertyEtl::address.name}.${AddressEtl::matchableAddress.name}"
        val callOutcomeField = "${InteractionEtl::callDetails.name}.${CallDetailsEtl::outcome.name}"
        val initiatedUtcTimestampField = InteractionEtl::initiatedUtcTimestamp.name
        val marketListing = "marketListing"
        val marketListingDateListed = "$marketListing.list_date"
        val agentAppraisalVisit = "$property.${PropertyEtl::appointments.name}.${AppointmentType.AGENT_APPRAISAL_VISIT.name}"
        val wonPhases: List<PropertyPhase> = listOf(CampaignPrep, Campaign, Withdrawn, Settlement, Complete)
        val wonPhasesString = wonPhases.joinToString(
            separator = ", ",
            prefix = "(",
            postfix = ")"
        ) { "'${it.name}'" }
        val agency = "agency"
        return """
            SELECT
            $agent.${AgentEtl::fullName.name} as $agentName,
            $agent.${AgentEtl::avatarUrl.name} as $agentAvatar,
            $property.$propertyFormattedAddressField as $address,
            $property.${PropertyEtl::id.name} as $propertyId,
            $marketListing.advertiser.name as $advertiserName,
            $marketListing.listing_slug as $listingSlug,
            $marketListing.address.latitude as $latitude,
            $marketListing.address.longitude as $longitude,
            CASE
                WHEN $marketListing.source = 'Domain' THEN CONCAT('${ListingSource.Domain.baseUrl}', $marketListing.listing_slug)
                WHEN $marketListing.source = 'TradeMe' THEN CONCAT('${ListingSource.TradeMe.baseUrl}', $marketListing.listing_slug)
                WHEN $marketListing.source = 'Reconz' THEN CONCAT('${ListingSource.Reconz.baseUrl}', $marketListing.listing_slug)
                WHEN $marketListing.source = 'REINZ' THEN NULL
                ELSE NULL
            END as $portalUrl,
            $marketListing.source as $listingSource,
            $marketListing.sold_date as $soldDate,
            DATE($agentAppraisalVisit, '$timezoneId') as $agentAppraisalDate,
            $leadScoreActivity.${LeadScoreActivityEtl::leadScore.name} as $leadScore,
            DATE($interaction.$initiatedUtcTimestampField, '$timezoneId') AS $connected,
            ($property.${PropertyEtl::phase.name} IN $wonPhasesString OR $agency.${AgencyEtl::name.name} = COALESCE($marketListing.advertiser.name, 'Unknown')) AS $won,
            DATE($marketListingDateListed, '$timezoneId') as $date,
            TIMESTAMP_ADD($marketListingDateListed, INTERVAL 7 DAY) > ${Utils.timestampMillis(endOfDay.millis)} as $inLast7Days,
            EXTRACT(YEAR FROM $marketListingDateListed AT TIME ZONE '$timezoneId') = EXTRACT(YEAR FROM CURRENT_TIMESTAMP() AT TIME ZONE '$timezoneId') as $inCurrentYear,
            $marketListingDateListed < ${Utils.timestampMillis(windows.primaryWindow.start.millis)} as $inPrevious,
            CASE
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.${InteractionEtl::type.name} = '${InteractionType.Call.name}' AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($marketListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($marketListingDateListed, '$timezoneId'))) THEN 'Not contacted'
                WHEN NOT EXISTS(SELECT * FROM $interactionTable AS $interaction WHERE $interaction.propertyId = $property.id AND $interaction.callDetails.outcome = "Spoke" AND (DATE($interaction.$initiatedUtcTimestampField , '$timezoneId') BETWEEN DATE_SUB(DATE($marketListingDateListed, '$timezoneId'), INTERVAL 3 MONTH) AND DATE($marketListingDateListed, '$timezoneId'))) THEN 'Attempted call, not connected'
                WHEN NOT EXISTS(SELECT * FROM $appraisalTable AS $appraisal WHERE $appraisal.propertyId = $property.id AND (DATE($appraisal.date, '$timezoneId') BETWEEN DATE_SUB(DATE($marketListingDateListed, '$timezoneId'), INTERVAL 12 MONTH) AND DATE($marketListingDateListed, '$timezoneId'))) THEN 'Connected, not appraised'
                ELSE 'Appraised'
            END AS $funnelStatus,
            FROM
            ${reportingFilter.teamAgentTableLine(org, bigQueryProjectId) ?: ""}
            `$agentTable` AS $agent,
            `$agencyTable` AS $agency,
            `$propertyTable` AS $property
            JOIN `$marketListingTable` AS $marketListing
                ON REPLACE(REPLACE($property.$propertyMatchableAddressField, ' ', ''), ',', '') = REPLACE(REPLACE(CONCAT(COALESCE($marketListing.address.unit_number, ''), ' ', COALESCE($marketListing.address.street_number, ''), ' ', COALESCE($marketListing.address.street_name, ''), ' ', COALESCE($marketListing.address.major_municipality, '')), ' ', ''), ',', '')
                AND $property.${PropertyEtl::created.name} < $marketListingDateListed
            LEFT JOIN `$interactionTable` AS $interaction
                ON $interaction.${InteractionEtl::propertyId.name} = $property.${PropertyEtl::id.name}
                AND $interaction.$initiatedUtcTimestampField < $marketListingDateListed
                AND $interaction.$callOutcomeField = '${CallOutcome.Spoke.name}'
            LEFT JOIN `$interactionTable` AS ${interaction}2
                ON $interaction.${InteractionEtl::propertyId.name} = ${interaction}2.${InteractionEtl::propertyId.name}
                AND $interaction.$initiatedUtcTimestampField < ${interaction}2.$initiatedUtcTimestampField
                AND ${interaction}2.$initiatedUtcTimestampField < $marketListingDateListed
                AND ${interaction}2.$callOutcomeField = '${CallOutcome.Spoke.name}'
            LEFT JOIN `$leadScoreActivityTable` AS $leadScoreActivity
                ON $leadScoreActivity.${LeadScoreActivityEtl::property.name} = $property.${PropertyEtl::id.name}
                AND $leadScoreActivity.$leadScoreActivityChangedField < TIMESTAMP_SUB($marketListingDateListed, INTERVAL 1 DAY)
            LEFT JOIN `$leadScoreActivityTable` AS ${leadScoreActivity}2
                ON $leadScoreActivity.${LeadScoreActivityEtl::property.name} = ${leadScoreActivity}2.${LeadScoreActivityEtl::property.name}
                AND $leadScoreActivity.$leadScoreActivityChangedField < ${leadScoreActivity}2.$leadScoreActivityChangedField
                AND ${leadScoreActivity}2.$leadScoreActivityChangedField < TIMESTAMP_SUB($marketListingDateListed, INTERVAL 1 DAY)
            WHERE $agent.${AgentEtl::id.name} = $property.${PropertyEtl::agent.name}
            AND $agent.${AgentEtl::business.name} = $property.${PropertyEtl::business.name}
            ${reportingFilter.teamAgentJoinLine() ?: ""}
            AND ${interaction}2.${InteractionEtl::propertyId.name} IS NULL
            AND ${leadScoreActivity}2.${LeadScoreActivityEtl::property.name} IS NULL
            AND $agency.${AgencyEtl::id.name} = $property.${PropertyEtl::agency.name}
            AND $marketListingDateListed >= ${Utils.timestampMillis(start.millis)}
            AND $marketListingDateListed < ${Utils.timestampMillis(end.millis)}
            AND $marketListing.listing_type IN ('Sale', 'Sold')
            AND (($property.${PropertyEtl::phase.name} IN $wonPhasesString) OR ($property.${PropertyEtl::database.name}) OR ($property.${PropertyEtl::phase.name} = 'LostAndSold'))
            ${reportingFilter.sqlFilter()}
            ORDER BY $date DESC
        """.trimIndent()
    }

    companion object {
        const val propertyId = "property_id"
        const val listingSlug = "listing_slug"
        const val listingSource = "listing_source"
        const val portalUrl = "portal_url"
        const val latitude = "latitude"
        const val longitude = "longitude"
        const val agentAppraisalDate = "agent_appraisal_date"
        const val connected = "connected"
        const val won = "won"
        const val soldDate = "soldDate"
    }
}
