package upside.bootstrap.controller

import com.nurturecloud.common.exception.NctException
import com.nurturecloud.common.exception.NctExceptionCode
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import nct.core.settings.DetermineHazardsAndRisksEnabled
import nct.core.settings.agency.buyermodule.FetchBuyerModuleEnabledByUserId
import nct.core.settings.business.FetchBusinessOpenDatabaseEnabledById
import nct.core.settings.business.FetchBusinessSmsEnabledById
import nct.core.settings.business.FetchBusinessSmsRepliesEnabledById
import nct.core.settings.businessuser.adapters.web.FetchSmsEnabledSetting
import nct.core.user.impersonate.adapters.web.ImpersonateUserController
import org.slf4j.LoggerFactory
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.web.authentication.rememberme.PersistentTokenBasedRememberMeServices
import org.springframework.web.bind.annotation.*
import upside.bootstrap.Common.guardOnlyDemoOrg
import upside.bootstrap.generator.TemplateGenerator
import upside.bootstrap.generator.regions.BootstrapRegions
import upside.bootstrap.model.BootstrapConfiguration
import upside.bootstrap.model.BootstrapTemplate
import upside.bootstrap.model.dto.AvailableStatesAndRolesDto
import upside.bootstrap.process.DemonstrationProcess
import upside.bootstrap.repository.DemonstrationUserRepository
import upside.controllers.dto.response.Ok
import upside.controllers.dto.user.UserDto
import upside.model.user.User
import upside.organisations.OrganisationService
import upside.process.UserProgress
import upside.service.security.SecurityUtils.getCurrentUser
import upside.service.security.SpringUserService
import java.util.*

@RestController
@RequestMapping(path = ["/api/v2"])
class DemonstrationController(
    private val demonstrationProcess: DemonstrationProcess,
    private val springUserService: SpringUserService,
    private val rememberMeService: PersistentTokenBasedRememberMeServices,
    private val userProgress: UserProgress,
    private val demonstrationUserRepository: DemonstrationUserRepository,
    private val fetchSmsEnabledSetting: FetchSmsEnabledSetting,
    private val fetchBuyerModuleEnabledByUserId: FetchBuyerModuleEnabledByUserId,
    private val fetchBusinessSmsEnabledById: FetchBusinessSmsEnabledById,
    private val fetchOpenDatabaseEnabledById: FetchBusinessOpenDatabaseEnabledById,
    private val determineHazardsAndRisksEnabled: DetermineHazardsAndRisksEnabled,
    private val fetchBusinessSmsRepliesEnabledById: FetchBusinessSmsRepliesEnabledById,
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @GetMapping(path = ["/demo/configuration/default"])
    fun getDefaultConfiguration(): BootstrapConfiguration =
        BootstrapConfiguration.defaultConfiguration(OrganisationService.getCurrentOrganisation())

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @RequestMapping(path = ["/demo/template/generate"], method = [RequestMethod.POST])
    fun generateBootstrapTemplate(@RequestBody config: BootstrapConfiguration): BootstrapTemplate {
        val org = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(org)
        return TemplateGenerator.createTemplate(org, config)
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @GetMapping(path = ["/demo/template/default"])
    fun generateDefaultBootstrapTemplate(): BootstrapTemplate {
        val org = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(org)
        return TemplateGenerator.createTemplate(org, BootstrapConfiguration.defaultConfiguration(OrganisationService.getCurrentOrganisation()))
    }

    // uses job as the job cloud run request times out in 10 minutes opposed the 1 minute for non-job endpoints
    @GetMapping(path = ["/job/demo/template/default"])
    fun generateDefaultBootstrapTemplateAsAJob(
        request: HttpServletRequest,
    ): BootstrapTemplate {
        val org = OrganisationService.orgByDomain(request.serverName)
        guardOnlyDemoOrg(org)
        return TemplateGenerator.createTemplate(org, BootstrapConfiguration.defaultConfiguration(org))
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @RequestMapping(path = ["/demo/template/import"], method = [RequestMethod.POST])
    fun importTemplate(
        @RequestBody template: BootstrapTemplate,
    ): Ok {
        demonstrationProcess.importTemplate(OrganisationService.getCurrentOrganisation(), template)
        return Ok.ok()
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @RequestMapping(path = ["/demo/template/generate-default-and-import"], method = [RequestMethod.POST])
    fun generateDefaultAndImportTemplate(): Ok = importTemplate(generateDefaultBootstrapTemplate())

    // uses job as the job cloud run request times out in 10 minutes opposed the 1 minute for non-job endpoints
    @GetMapping(path = ["/job/demo/template/generate-default-and-import"])
    fun generateDefaultAndImportTemplateForOrg(
        request: HttpServletRequest,
    ): Ok {
        val org = OrganisationService.orgByDomain(request.serverName)
        guardOnlyDemoOrg(org)
        val template = TemplateGenerator.createTemplate(org, BootstrapConfiguration.defaultConfiguration(org))
        demonstrationProcess.importTemplate(org, template)
        return Ok.ok()
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @GetMapping(path = ["/demo/available-states-and-roles"])
    fun availableStatesAndRoles(): AvailableStatesAndRolesDto {
        val currentOrg = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(currentOrg)

        val region = BootstrapRegions.getByOrg(currentOrg)
        return AvailableStatesAndRolesDto.fromRegion(region)
    }

    /**
     * Adapted from [upside.controllers.UserController.signInAsUser]
     */
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @GetMapping(path = ["/demo/impersonate/state/{state}/role/{rawPrimaryRole}"])
    fun impersonateBusinessUser(
        @PathVariable state: String,
        @PathVariable rawPrimaryRole: String,
        @RequestHeader(required = false, name = "User-Agent") userAgent: String?,
        request: HttpServletRequest,
        response: HttpServletResponse,
    ): UserDto {
        val primaryRole = rawPrimaryRole.lowercase()
        val org = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(org)
        val currentUser = getCurrentUser()
        val impersonatedUser: User? = when (rawPrimaryRole.lowercase()) {
            User.Roles.Agent -> demonstrationUserRepository.getAgentByState(state)
            // only one concierge admin per org
            User.Roles.ConciergeAdmin -> demonstrationUserRepository.getConciergeAdmin(org.name)
            else -> demonstrationUserRepository.getByStateAndRole(state, primaryRole)
        }

        if (impersonatedUser != null) {
            log.info("Found user ${impersonatedUser.id}")
            // Prevents the case of the impersonated and real sessions from having the same userAndDeviceId
            val impersonatedUserAgent = "$userAgent Impersonated"
            springUserService.logout(request, response)
            rememberMeService.loginSuccess(request, response, springUserService.authentication(impersonatedUser, impersonatedUserAgent))
            userProgress.userImpersonated(impersonatedUser.id, currentUser)
            log.info("${currentUser.email} was successfully impersonated as ${impersonatedUser.email}")
            ImpersonateUserController.addImpersonatorCookie(currentUser, response)
            val businessId = impersonatedUser.inferAssignedBusinessId()
            val smsEnabled = businessId?.let { fetchSmsEnabledSetting(impersonatedUser.id, it) } ?: false
            val openDatabaseEnabled = fetchOpenDatabaseEnabled(impersonatedUser)
            return UserDto(
                impersonatedUser,
                true,
                null,
                smsEnabled,
                fetchBuyerModuleEnabled(impersonatedUser),
                fetchBusinessSmsEnabled(businessId),
                false,
                openDatabaseEnabled.openDatabaseEnabled,
                openDatabaseEnabled.openBuyerDatabaseEnabled,
                determineHazardsAndRisksEnabled.isHazardsAndRisksEnabled(impersonatedUser),
                fetchBusinessSmsRepliesEnabled(businessId),
            )
        } else {
            log.info("Couldn't find matching user")
        }

        log.warn("${currentUser.email} failed to impersonate $primaryRole user for $state")
        throw NctException(NctExceptionCode.UNAUTHORIZED)
    }

    private fun fetchSmsEnabledSetting(userId: UUID, businessId: UUID): Boolean =
        when (val outcome = fetchSmsEnabledSetting.fetch(userId, businessId)) {
            is FetchSmsEnabledSetting.Outcome.Failed -> throw outcome.cause
            is FetchSmsEnabledSetting.Outcome.UserNotFound -> throw NctException(NctExceptionCode.USER_NOT_FOUND)
            is FetchSmsEnabledSetting.Outcome.Succeeded  -> outcome.smsEnabled
        }

    private fun fetchBusinessSmsEnabled(businessId: UUID?): Boolean = businessId
        ?.let {
            when (fetchBusinessSmsEnabledById.fetch(it)) {
                is FetchBusinessSmsEnabledById.FetchOutcome.Enabled -> true
                is FetchBusinessSmsEnabledById.FetchOutcome.Disabled -> false
            }
        }
        ?: false

    private fun fetchOpenDatabaseEnabled(user: User): FetchBusinessOpenDatabaseEnabledById.Settings =
        user.inferAssignedBusinessId()?.let { fetchOpenDatabaseEnabledById.fetch(it) } ?:
         FetchBusinessOpenDatabaseEnabledById.Settings(openDatabaseEnabled = false, openBuyerDatabaseEnabled = false)

    private fun fetchBusinessSmsRepliesEnabled(businessId: UUID?): Boolean = businessId
        ?.let {
            when (fetchBusinessSmsRepliesEnabledById.fetch(it)) {
                is FetchBusinessSmsRepliesEnabledById.FetchOutcome.Enabled -> true
                is FetchBusinessSmsRepliesEnabledById.FetchOutcome.Disabled -> false
            }
        }
        ?: false

    // These endpoints are alternate ways to trigger the various jobs using User auth instead of via a job endpoint
    // They infer org from caller
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @GetMapping(path = ["/demo/regenerate/trigger"])
    fun triggerRegenerateEnvironment(): Ok {
        val org = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(org)
        demonstrationProcess.triggerRegenerateEnvironment(org, bootstrapId = null)
        return Ok.ok()
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @GetMapping(path = ["/demo/create/property-dependant-data/trigger"])
    fun triggerCreatePropertyDependantData(): Ok {
        val org = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(org)
        demonstrationProcess.triggerCreatePropertyDependantData(org, bootstrapId = null)
        return Ok.ok()
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @GetMapping(path = ["/demo/deleteAll/trigger"])
    fun triggerDeleteEnvironment(): Ok {
        val org = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(org)
        demonstrationProcess.triggerDeleteEnvironment(org, bootstrapId = null)
        return Ok.ok()
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_DEMO')")
    @RequestMapping(path = ["/demo/template/clear/trigger"], method = [RequestMethod.GET, RequestMethod.POST])
    fun triggerClearTemplate(): Ok {
        val org = OrganisationService.getCurrentOrganisation()
        guardOnlyDemoOrg(org)
        demonstrationProcess.triggerClearTemplate(org)
        return Ok.ok()
    }

    private fun fetchBuyerModuleEnabled(user: User): Boolean = fetchBuyerModuleEnabledByUserId.fetch(user.id)
}
