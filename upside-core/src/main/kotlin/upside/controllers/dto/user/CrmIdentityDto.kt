package upside.controllers.dto.user

import com.google.gson.annotations.SerializedName
import upside.core.businessContact.utility.BusinessContactDisplayNameUtils
import upside.core.location.converter.AddressConverter.toTaggedAddressDto
import upside.core.location.dto.AddressDto
import upside.model.HasOrganisationUtils.organisation
import upside.model.business.BusinessContactFieldString
import upside.model.business.BusinessContactOtherFieldNames
import upside.model.business.BusinessContactTag
import upside.model.business.BusinessContactType
import upside.model.business.BusinessContactUnsubscribe
import upside.model.business.TaggedAddress.Dto
import upside.model.user.User
import upside.utils.BusinessContactEmailUtils
import upside.utils.StringUtils.EMPTY_STRING
import java.util.*

class CrmIdentityDto(
    user: User,
    businessId: UUID?,
    vaultExternalIds: List<String>,
    canSeeSmsFeature: Boolean,
    buyerModuleEnabled: <PERSON><PERSON><PERSON>,
    businessSmsEnabled: <PERSON><PERSON><PERSON>,
    activePipeBulkEmailEnabled: Boolean,
    openDatabaseEnabled: <PERSON><PERSON>an,
    openBuyerDatabaseEnabled: <PERSON><PERSON>an,
    hazardsAndRisksEnabled: <PERSON>olean,
    smsRepliesEnabled: Boolean,
    acceptedTermsAndConditions: Boolean
) : IdentityDto(
    user,
    canSeeSmsFeature,
    buyerModuleEnabled,
    businessSmsEnabled,
    activePipeBulkEmailEnabled,
    openDatabaseEnabled,
    openBuyerDatabaseEnabled,
    hazardsAndRisksEnabled,
    smsRepliesEnabled,
    acceptedTermsAndConditions
) {
    @SerializedName("platform")
    var isPlatform: Boolean
    var isShadow: Boolean
    var crmFirstName: String
    var crmLastName: String
    var otherNames: List<BusinessContactOtherFieldNames>
    var legalName: String?
    var businessContactType: BusinessContactType?
    var crmPhoneNumber: String
    var crmPhoneNumberTag: BusinessContactTag?
    var crmPhoneNumberComment: String?
    var otherPhoneNumbers: List<BusinessContactFieldString>
    var crmPhoneNumberAreaCode: String
    var archived: Boolean
    var otherEmails: List<BusinessContactFieldString>
    var primaryStructuredAddress: AddressDto?
    var otherStructuredAddresses: List<Dto>
    var company: String
    var greeting: String
    @Deprecated("Use unsubscribe.call instead")
    var doNotContact: Boolean
    var vaultExternalIds: List<String>
    var displayName: String?
    var unsubscribe: BusinessContactUnsubscribe?

    fun setPlatform(platform: Boolean): CrmIdentityDto {
        isPlatform = platform
        return this
    }

    fun setShadow(shadow: Boolean): CrmIdentityDto {
        isShadow = shadow
        return this
    }

    fun setCrmFirstName(crmFirstName: String): CrmIdentityDto {
        this.crmFirstName = crmFirstName
        return this
    }

    fun setCrmLastName(crmLastName: String): CrmIdentityDto {
        this.crmLastName = crmLastName
        return this
    }

    fun setOtherNames(otherNames: List<BusinessContactOtherFieldNames>): CrmIdentityDto {
        this.otherNames = otherNames
        return this
    }

    fun setOtherEmails(otherEmails: List<BusinessContactFieldString>): CrmIdentityDto {
        this.otherEmails = otherEmails
        return this
    }

    fun setType(type: BusinessContactType): CrmIdentityDto {
        this.businessContactType = type
        return this
    }

    fun setCompany(company: String): CrmIdentityDto {
        this.company = company
        return this
    }

    init {
        val crmDetails = user.getCrmDetails(businessId)
        isPlatform = user.isPlatformUser
        isShadow = user.shadow
        crmFirstName = crmDetails.firstName
        crmLastName = crmDetails.lastName
        otherNames = crmDetails.otherNames
        legalName = crmDetails.legalName
        businessContactType = crmDetails.businessContactType
        crmPhoneNumber = when(val crmPhoneNumber = crmDetails.phoneNumber) {
            EMPTY_STRING -> {
                crmPhoneNumberTag = null
                crmPhoneNumberComment = null
                crmPhoneNumberAreaCode = EMPTY_STRING
                when (user.organisation().featureConfiguration.displayPlatformPhoneIfCrmNull) {
                    true -> user.platformPhoneNumber ?: EMPTY_STRING
                    false -> EMPTY_STRING
                }
            }
            else -> {
                crmPhoneNumberTag = crmDetails.phoneNumberTag
                crmPhoneNumberComment = crmDetails.phoneNumberComment
                crmPhoneNumberAreaCode = crmDetails.phoneNumberAreaCode
                crmPhoneNumber
            }
        }
        otherPhoneNumbers = crmDetails.otherPhoneNumbers
        email = BusinessContactEmailUtils.inferPrimaryEmail(this.email, crmDetails.primaryEmail)
        otherEmails = crmDetails.otherEmails
        primaryStructuredAddress = crmDetails.primaryStructuredIntlAddress
            ?.let { AddressDto(crmDetails.primaryStructuredIntlAddress, null) }
        otherStructuredAddresses = crmDetails.otherStructuredIntlAddresses.map { it.toTaggedAddressDto() }
        dateOfBirth = crmDetails.dateOfBirth
        company = crmDetails.company
        greeting = crmDetails.greeting
        doNotContact = crmDetails.doNotContact
        authenticated = null
        archived = crmDetails.archived
        this.vaultExternalIds = vaultExternalIds
        displayName = BusinessContactDisplayNameUtils.inferDisplayName(user, crmDetails)
        unsubscribe = crmDetails.unsubscribe

        if (businessId != null && user.isVendorOrBuyer) {
            firstName = null
            lastName = null
            phoneNumber = null
            phoneNumberAreaCode = null
        }
    }
}
