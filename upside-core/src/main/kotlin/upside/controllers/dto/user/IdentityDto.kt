package upside.controllers.dto.user

import org.joda.time.DateTime
import upside.controllers.dto.business.BusinessDto
import upside.controllers.dto.property.MediaDto
import upside.model.property.media.Media
import upside.model.user.User
import upside.utils.OptionalUtils.orNull
import upside.utils.StreamUtils.sorted

/**
 * CLASS DE-LOMBOKED AND KOTLINIZED
 */
open class IdentityDto : SimpleUserDto {
    var roles: List<String> = ArrayList()
    var dateOfBirth: DateTime? = null
    var homeAddress: String? = null
    var authenticated: Boolean? = null
    var emailConfirmed: Boolean? = null
    var phoneVerified: Boolean? = null
    var created: DateTime? = null
    var avatar: MediaDto? = null
    var business: BusinessDto? = null
    var canSeeSmsFeature: Boolean = false
    var buyerModuleEnabled: Boolean = false
    var businessSmsEnabled: Boolean = false
    var activePipeBulkEmailEnabled: Boolean = false
    var openDatabaseEnabled: Boolean = false
    var openBuyerDatabaseEnabled: Boolean = false
    var hazardsAndRisksEnabled: Boolean = false
    var smsRepliesEnabled: Boolean = false
    var acceptedTermsAndConditions: Boolean? = null

    private constructor() {
        authenticated = false
        acceptedTermsAndConditions = false
    }

    constructor(
        user: User,
        canSeeSmsFeature: Boolean,
        buyerModuleEnabled: Boolean,
        businessSmsEnabled: Boolean,
        activePipeBulkEmailEnabled: Boolean,
        openDatabaseEnabled: Boolean,
        openBuyerDatabaseEnabled: Boolean,
        hazardsAndRisksEnabled: Boolean,
        smsRepliesEnabled: Boolean,
        acceptedTermsAndConditions: Boolean
    ) : super(
        user.id,
        user.email,
        user.platformFirstName,
        user.platformLastName,
        user.platformPhoneNumber,
        user.platformPhoneNumberAreaCode,
        consent = null,
    ) {
        this.acceptedTermsAndConditions = acceptedTermsAndConditions
        authenticated = true
        created = user.created
        roles = ArrayList(user.roles)
        roles = sorted(
            roles,
            java.util.Comparator { obj: String, anotherString: String? ->
                obj.compareTo(
                    anotherString!!
                )
            }
        )
        avatar = orNull(user.avatar, { media: Media -> MediaDto(media) })
        emailConfirmed = user.isConfirmed
        phoneVerified = user.isPhoneVerified
        dateOfBirth = user.dateOfBirth
        homeAddress = user.getHomeAddress()

        business = user
            .inferAssignedBusiness()
            ?.let { BusinessDto(it) }

        this.canSeeSmsFeature = canSeeSmsFeature
        this.buyerModuleEnabled = buyerModuleEnabled
        this.businessSmsEnabled = businessSmsEnabled
        this.activePipeBulkEmailEnabled = activePipeBulkEmailEnabled
        this.openDatabaseEnabled = openDatabaseEnabled
        this.openBuyerDatabaseEnabled = openBuyerDatabaseEnabled
        this.hazardsAndRisksEnabled = hazardsAndRisksEnabled
        this.smsRepliesEnabled = smsRepliesEnabled
    }

    companion object {
        @JvmField
        var UNAUTHENTICATED = IdentityDto()
    }
}
