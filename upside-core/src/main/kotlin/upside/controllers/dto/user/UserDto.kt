package upside.controllers.dto.user

import org.apache.commons.lang3.StringUtils
import org.apache.commons.validator.routines.EmailValidator
import org.joda.time.DateTime
import upside.controllers.dto.business.BusinessDto
import upside.controllers.dto.property.MediaDto
import upside.controllers.validation.Validatable
import upside.controllers.validation.Validation
import upside.model.business.Business
import upside.model.buyer.BuyerDetailsDto
import upside.model.property.media.Media
import upside.model.user.User
import upside.utils.OptionalUtils.orNull
import upside.utils.StreamUtils.sorted
import java.util.*

/**
 * CLASS DE-LOMBOKED AND KOTLINIZED
 */
open class UserDto : SimpleUserDto, Validatable {
    var roles: List<String> = ArrayList()
    var dateOfBirth: DateTime? = null
    var homeAddress: String? = null
    var authenticated: Boolean? = false
    var emailConfirmed = false
    var phoneVerified: Boolean? = null
    var created: DateTime? = null
    var avatar: MediaDto? = null
    @Deprecated("Use BusinessContact.unsubscribe.call instead")
    var doNotContact: Boolean? = null
    var business: BusinessDto? = null
    var canSeeSmsFeature: Boolean = false
    var buyerModuleEnabled: Boolean = false
    var businessSmsEnabled: Boolean = false
    var activePipeBulkEmailEnabled: Boolean = false
    var openDatabaseEnabled: Boolean = false
    var openBuyerDatabaseEnabled: Boolean = false
    var hazardsAndRisksEnabled: Boolean = false
    var smsRepliesEnabled: Boolean = false

    constructor(user: User, authenticated: Boolean?, businessId: UUID?) : super(user, businessId) {
        this.authenticated = authenticated
        created = user.created
        roles = ArrayList(user.roles)
        roles = sorted(
            roles,
            Comparator { obj: String, anotherString: String? ->
                obj.compareTo(
                    anotherString!!
                )
            }
        )
        avatar = orNull(user.avatar) { media: Media -> MediaDto(media) }
        emailConfirmed = user.isConfirmed
        phoneVerified = user.isPhoneVerified
        dateOfBirth = user.dateOfBirth
        homeAddress = user.getHomeAddress()
        doNotContact = user.isDoNotContact(businessId)
        business = Optional.ofNullable(user.inferAssignedBusiness()).map { business: Business? ->
            BusinessDto(
                business!!
            )
        }.orElse(null)
    }

    constructor(
        user: User,
        authenticated: Boolean?,
        businessId: UUID?,
        canSeeSmsFeature: Boolean,
        buyerModuleEnabled: Boolean,
        businessSmsEnabled: Boolean,
        activePipeBulkEmailEnabled: Boolean,
        openDatabaseEnabled: Boolean,
        openBuyerDatabaseEnabled: Boolean,
        hazardsAndRisksEnabled: Boolean,
        smsRepliesEnabled: Boolean
    ) : this(user, authenticated, businessId) {
        this.canSeeSmsFeature = canSeeSmsFeature
        this.buyerModuleEnabled = buyerModuleEnabled
        this.businessSmsEnabled = businessSmsEnabled
        this.activePipeBulkEmailEnabled = activePipeBulkEmailEnabled
        this.openDatabaseEnabled = openDatabaseEnabled
        this.openBuyerDatabaseEnabled = openBuyerDatabaseEnabled
        this.hazardsAndRisksEnabled = hazardsAndRisksEnabled
        this.smsRepliesEnabled = smsRepliesEnabled
    }

    constructor(user: User, businessId: UUID?) : this(user, null, businessId)

    constructor(
        firstName: String?,
        lastName: String?,
        email: String?,
        phoneNumber: String?,
        phoneNumberAreaCode: String?,
        doNotContact: Boolean?,
        consent: Boolean?,
    ) : super(
        email,
        firstName,
        lastName,
        phoneNumber,
        phoneNumberAreaCode,
    ) {
        this.doNotContact = doNotContact
        this.consent = consent
    }

    constructor(
        firstName: String?,
        lastName: String?,
        email: String?,
        phoneNumber: String?,
        phoneNumberAreaCode: String?,
        doNotContact: Boolean?,
    ) : super(
        email,
        firstName,
        lastName,
        phoneNumber,
        phoneNumberAreaCode,
    ) {
        this.doNotContact = doNotContact
    }

    constructor(dto: BuyerDetailsDto) : super(
        dto.email,
        dto.firstName,
        dto.lastName,
        dto.phoneNumber,
        dto.phoneNumberAreaCode
    ) {
        doNotContact = dto.doNotContact
    }

    @JvmOverloads
    constructor(
        email: String?,
        firstName: String?,
        lastName: String?,
        phoneNumber: String?,
        phoneNumberArea: String? = null
    ) : super(email, firstName, lastName, phoneNumber, phoneNumberArea)


    constructor(
        email: String?,
        firstName: String?,
        lastName: String?,
        phoneNumber: String?,
        doNotContact: Boolean?,
    ) : super(email, firstName, lastName, phoneNumber) {
        this.doNotContact = doNotContact
    }

    constructor()

    override fun validate(validation: Validation): Validation {
        return validation
            .validate(StringUtils.isNotBlank(email) || StringUtils.isNotBlank(phoneNumber), "email", "email-required")
            .validate(StringUtils.isNotBlank(firstName) || StringUtils.isNotBlank(lastName), "user", "name-required")
            .validateIfNotNull(email, { e: String? -> EmailValidator.getInstance().isValid(e) }, "email", "invalid")
    }

    fun setDateOfBirth(dateOfBirth: DateTime?): UserDto {
        this.dateOfBirth = dateOfBirth
        return this
    }

    fun setHomeAddress(homeAddress: String?): UserDto {
        this.homeAddress = homeAddress
        return this
    }

    fun setCreated(created: DateTime?): UserDto {
        this.created = created
        return this
    }

    fun builderSetAvatar(avatar: MediaDto?): UserDto {
        this.avatar = avatar
        return this
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as UserDto

        if (roles != other.roles) return false
        if (dateOfBirth != other.dateOfBirth) return false
        if (homeAddress != other.homeAddress) return false
        if (authenticated != other.authenticated) return false
        if (emailConfirmed != other.emailConfirmed) return false
        if (phoneVerified != other.phoneVerified) return false
        if (created != other.created) return false
        if (avatar != other.avatar) return false
        if (doNotContact != other.doNotContact) return false
        if (business != other.business) return false
        if (canSeeSmsFeature != other.canSeeSmsFeature) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + roles.hashCode()
        result = 31 * result + (dateOfBirth?.hashCode() ?: 0)
        result = 31 * result + (homeAddress?.hashCode() ?: 0)
        result = 31 * result + (authenticated?.hashCode() ?: 0)
        result = 31 * result + emailConfirmed.hashCode()
        result = 31 * result + (phoneVerified?.hashCode() ?: 0)
        result = 31 * result + (created?.hashCode() ?: 0)
        result = 31 * result + (avatar?.hashCode() ?: 0)
        result = 31 * result + (doNotContact?.hashCode() ?: 0)
        result = 31 * result + (business?.hashCode() ?: 0)
        result = 31 * result + canSeeSmsFeature.hashCode()
        return result
    }
}
