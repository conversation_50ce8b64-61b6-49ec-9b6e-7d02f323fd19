package upside.controllers

import com.nurturecloud.common.exception.NctException
import com.nurturecloud.common.exception.NctExceptionCode
import com.nurturecloud.common.validation.konform.ValidationResult
import com.nurturecloud.common.validation.konform.toErrorMessageMap
import nct.core.agent.AgentService
import nct.core.buyer.adapters.web.RegisterBuyerApiRequest
import nct.core.permissions.property.DeterminePropertiesAccessibleByUser
import nct.core.user.UpdateUserDetails
import nct.core.user.models.SimpleUpdateUserRequest
import org.joda.time.DateTime
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod.GET
import org.springframework.web.bind.annotation.RequestMethod.POST
import org.springframework.web.bind.annotation.RequestMethod.PUT
import org.springframework.web.bind.annotation.RestController
import upside.controllers.dto.buyer.BuyerPreferenceDto
import upside.controllers.dto.buyer.BuyerSearchRequest
import upside.controllers.dto.buyer.UpdateBuyerPreferenceDto
import upside.controllers.dto.property.PropertiesDto
import upside.controllers.dto.user.CrmIdentityDto
import upside.core.property.service.PropertyService
import upside.model.buyer.BuyerPreference.buyerPreferenceMatch
import upside.organisations.OrganisationService.getCurrentOrganisation
import upside.process.BuyerProcess
import upside.process.names.WorkflowName.createBuyer
import upside.repository.BuyerPreferenceRepository.Companion.searchBuyerPreference
import upside.repository.SuburbRepository
import upside.service.BuyerService
import upside.service.security.SecurityUtils.getCurrentBusinessIdForInternalUserOrNull
import upside.service.security.SecurityUtils.getCurrentUser
import upside.service.security.acl.AclPermissionPresets.GENERIC_INTERNAL_AGENT_PLUS_ABOVE_PLUS_ASSIGNED_CONCIERGE
import upside.service.security.acl.AclPermissionPresets.USER_INTERNAL_AGENT_PLUS_ABOVE
import upside.types.property.classifications.PropertyPhase
import upside.utils.OptionalUtils.found
import java.util.*

@RestController
@RequestMapping("/api/v2")
class BuyerController(
    private val propertyService: PropertyService,
    private val buyerProcess: BuyerProcess,
    private val buyerService: BuyerService,
    private val agentService: AgentService,
    private val suburbRepository: SuburbRepository,
    private val updateUserDetails: UpdateUserDetails,
    private val determinePropertiesAccessibleByUser: DeterminePropertiesAccessibleByUser,
) {
    @RequestMapping(value = ["/buyer/search"], method = [POST])
    @PreAuthorize(GENERIC_INTERNAL_AGENT_PLUS_ABOVE_PLUS_ASSIGNED_CONCIERGE)
    fun searchBuyers(@RequestBody searchRequest: BuyerSearchRequest): BuyerSearchResponse {
        searchRequest.validate()

        val user = getCurrentUser()

        if (user.isAgent) {
            val agent = found(agentService.get(user))
            if (!agentService.getAgentActiveSuburbs(agent).contains(searchRequest.getSuburbRef())) {
                throw NctException(NctExceptionCode.AGENT_SUBURB_RESTRICTED)
            }
        }

        val suburbs = mutableListOf(searchRequest.getSuburbRef())

        if (searchRequest.surroundingSuburbs == true) {
            suburbs.addAll(searchRequest.getSuburbRef().get().surroundingSuburbs.mapNotNull { it.suburb })
        }
        val result = searchBuyerPreference(suburbs, 1000).list()
        val businessId = getCurrentBusinessIdForInternalUserOrNull()
        return BuyerSearchResponse(result.filter { buyerPreferenceMatch(searchRequest).apply(it) }.map { BuyerPreferenceDto.fromBuyerPreference(it, businessId) })
    }

    @RequestMapping(value = ["/buyers/"], method = [POST])
    @PreAuthorize(GENERIC_INTERNAL_AGENT_PLUS_ABOVE_PLUS_ASSIGNED_CONCIERGE)
    fun registerBuyer(@RequestBody registerBuyerApiRequest: RegisterBuyerApiRequest): CrmIdentityDto {
        val dto = registerBuyerApiRequest.validateUserContactDetailsOrThrow().validateAndConstruct().let { when (it) {
            is ValidationResult.Invalid -> throw NctException(code = NctExceptionCode.VALIDATION_FAILED, errors = it.errors.toErrorMessageMap())
            is ValidationResult.Valid -> it.value
        } }

        val actor = getCurrentUser()

        return CrmIdentityDto(
            user = buyerProcess.createBuyer(
                org = getCurrentOrganisation(),
                dto = dto,
                actor = actor,
                business = actor.inferAssignedBusiness()
            ).user,
            businessId = getCurrentBusinessIdForInternalUserOrNull(),
            vaultExternalIds = emptyList(),
            canSeeSmsFeature = false,
            buyerModuleEnabled = false,
            businessSmsEnabled = false,
            activePipeBulkEmailEnabled = false,
            openDatabaseEnabled = false,
            openBuyerDatabaseEnabled = false,
            hazardsAndRisksEnabled = false,
            smsRepliesEnabled = false,
            acceptedTermsAndConditions = false
        )
    }

    @RequestMapping(value = ["/buyers/{userId}"], method = [POST])
    @PreAuthorize(GENERIC_INTERNAL_AGENT_PLUS_ABOVE_PLUS_ASSIGNED_CONCIERGE)
    fun registerAndUpdateBuyerDetails(
        @PathVariable userId: UUID,
        @RequestBody request: SimpleUpdateUserRequest
    ): Any {
        val actor = getCurrentUser()

        if (request.email.isNullOrBlank() && request.phone.isNullOrBlank())
            throwValidationException(mutableMapOf("email" to "Email or phone must be provided"))

        return when (val businessId = actor.inferAssignedBusinessId()) {
            null -> ResponseEntity.badRequest().body("")
            else -> {
                val business = actor.inferAssignedBusiness()
                val outcome = updateUserDetails.update(
                    UpdateUserDetails.Input(
                        userId = userId,
                        businessId = businessId,
                        organisation = getCurrentOrganisation(),
                        actor = actor,
                        updateUserRequest = SimpleUpdateUserRequest(
                            email = request.email,
                            firstName = request.firstName,
                            lastName = request.lastName,
                            phone = request.phone,
                            consent = request.consent,
                        ),
                        workflowName = createBuyer,
                        createdAt = DateTime.now(),
                        additionalSteps = { w -> w
                            .excludeInternal("Staff users cannot be a buyer")
                            .appendToAccessibleBy(w.actor)
                            .buyer()
                            .buyerPreferenceSuburbs(ArrayList())
                            .setBusinessContactUnsubscribe(business, null, request.consent)
                        }
                    )
                )

                when (outcome) {
                    is UpdateUserDetails.Outcome.Updated -> ResponseEntity.ok().build<String>()
                    is UpdateUserDetails.Outcome.FailedToUpdated.InvalidEmail -> throwValidationException(mutableMapOf("email" to "Invalid email"))
                    is UpdateUserDetails.Outcome.FailedToUpdated.InvalidPhoneNumber -> throwValidationException(mutableMapOf("phone number" to "Invalid phone number"))
                    is UpdateUserDetails.Outcome.FailedToUpdated.UserNotFound -> ResponseEntity.notFound().build<String>()
                    is UpdateUserDetails.Outcome.FailedToUpdated.BusinessNotFound -> ResponseEntity.notFound().build<String>()
                }
            }
        }
    }

    private fun throwValidationException(errors: MutableMap<String, String>) {
        throw NctException(
            code = NctExceptionCode.VALIDATION_FAILED,
            errors = errors as LinkedHashMap,
        )
    }

    @RequestMapping(value = ["/buyer/{id}/preference"], method = [GET])
    @PreAuthorize(USER_INTERNAL_AGENT_PLUS_ABOVE)
    fun getBuyerPreference(@PathVariable id: UUID): BuyerPreferenceDto {
        val buyer = buyerService.get(id) { throw NctException(NctExceptionCode.UNKNOWN_USER) }
        return BuyerPreferenceDto.fromBuyerPreference(found(buyerService.getPreference(buyer)), getCurrentBusinessIdForInternalUserOrNull())
    }

    // bingo
    @RequestMapping(value = ["/buyer/{id}/preference"], method = [PUT])
    @PreAuthorize(USER_INTERNAL_AGENT_PLUS_ABOVE)
    fun updateBuyerPreference(@PathVariable id: UUID, @RequestBody dto: UpdateBuyerPreferenceDto): BuyerPreferenceDto {
        dto.validate()
        val suburbs = dto.suburbs!!.toSet()
        if (suburbRepository.get(suburbs.toList()).count() != suburbs.size) {
            throw NctException(NctExceptionCode.SUBURBS_NOT_FOUND, (dto.suburbs ?: listOf()).joinToString(","))
        }
        val buyer = buyerProcess.updatePreferences(id, dto, getCurrentUser())
        return BuyerPreferenceDto.fromBuyerPreference(found(buyerService.getPreference(buyer)), getCurrentBusinessIdForInternalUserOrNull())
    }

    @RequestMapping(value = ["/buyer/{id}/properties"], method = [GET])
    @PreAuthorize(USER_INTERNAL_AGENT_PLUS_ABOVE)
    fun getBuyerProperties(@PathVariable id: UUID): PropertiesDto {
        val buyer = buyerService.get(id) ?: throw NctException(NctExceptionCode.UNKNOWN_USER)
        var properties = propertyService.getBuyerProperties(buyer, getCurrentBusinessIdForInternalUserOrNull())
        val propertyIds =  properties.map { it.id }.toSet()

        if (!getCurrentUser().isAdmin) { properties = properties.filterNot { it.phase == PropertyPhase.Deleted } }

        properties = determinePropertiesAccessibleByUser.determineAccessibleProperties(
            userId = getCurrentUser().id,
            propertyIds = propertyIds
        ).mapNotNull { properties.find { property -> property.id == it } }

        return PropertiesDto(
            properties.sortedWith(compareByDescending { it.created }).map { it to emptyList() }
        )
    }

    data class BuyerSearchResponse(val buyers: List<BuyerPreferenceDto>)
}
