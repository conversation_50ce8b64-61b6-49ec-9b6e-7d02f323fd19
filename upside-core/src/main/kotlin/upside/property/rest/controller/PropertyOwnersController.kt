package upside.property.rest.controller

import com.nurturecloud.common.exception.NctException
import com.nurturecloud.common.exception.NctExceptionCode
import nct.core.businesscontact.BusinessContactByUserAndBusiness
import nct.core.businesscontact.FetchBusinessContactByBusinessIdAndUserId
import nct.core.businesscontact.FetchBusinessContactsByUserIdsAndBusinessIds
import nct.core.property.FetchPropertyById
import nct.core.property.FetchPropertyContactRelationships
import nct.core.property.owners.DeleteOwnersOfAProperty
import nct.core.user.UpdateUserDetails
import nct.core.user.UpdateUserDetails.Outcome
import nct.core.user.models.SimpleUpdateUserRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RestController
import upside.controllers.dto.user.CrmIdentityDto
import upside.model.HasOrganisationUtils.organisation
import upside.process.ProgressProcess
import upside.process.names.WorkflowName
import upside.property.rest.dto.PropertyOwnersResponseDto
import upside.service.security.SecurityUtils
import upside.service.security.acl.AclPermissionPresets
import upside.types.property.contacts.PropertyRelationContactTag
import java.util.Locale
import java.util.UUID

@RestController
@RequestMapping("/api/v2")
class PropertyOwnersController(
    private val progressProcess: ProgressProcess,
    private val deletePropertyOwnersOfAProperty: DeleteOwnersOfAProperty,
    private val fetchBusinessContactByBusinessIdAndUserId: FetchBusinessContactByBusinessIdAndUserId,
    private val fetchPropertyById: FetchPropertyById,
    private val fetchPropertyContactRelationships: FetchPropertyContactRelationships,
    private val fetchBusinessContactsByUserIdsAndBusinessIds: FetchBusinessContactsByUserIdsAndBusinessIds,
    private val updateUserDetails: UpdateUserDetails,
) {

    @RequestMapping(path = ["/property/{id}/additional-owners"], method = [RequestMethod.GET])
    @PreAuthorize(AclPermissionPresets.PROPERTY_INTERNAL_AGENT_PLUS_ABOVE_INCLUDE_OFI_AGENT)
    fun getOwners(@PathVariable id: UUID): PropertyOwnersResponseDto<CrmIdentityDto> = getOwnersDto(id)

    @RequestMapping(path = ["/property/{id}/additional-owners/user/{userId}"], method = [RequestMethod.PUT])
    @PreAuthorize(AclPermissionPresets.PROPERTY_INTERNAL_AGENT_PLUS_ABOVE_INCLUDE_OFI_AGENT)
    fun addOwner(
        @PathVariable id: UUID,
        @PathVariable userId: UUID,
        @RequestBody simpleUpdateUserRequest: SimpleUpdateUserRequest? = null
    ): PropertyOwnersResponseDto<CrmIdentityDto> {

        val property = fetchPropertyOrNull(id) ?: throw NctException(NctExceptionCode.PROPERTY_NOT_FOUND)
        val propertyBusiness = property.business ?: throw NctException(NctExceptionCode.BUSINESS_NOT_FOUND)

        val businessContact = when (val outcome = fetchBusinessContactByBusinessIdAndUserId.fetch(userId = userId, businessId = propertyBusiness.getUUID())) {
            is FetchBusinessContactByBusinessIdAndUserId.FetchOutcome.NotFound -> null
            is FetchBusinessContactByBusinessIdAndUserId.FetchOutcome.Found -> outcome.businessContact
        }
        val businessContactUser = businessContact?.user?.get() ?: throw NctException(NctExceptionCode.USER_NOT_FOUND)

        if (!businessContactUser.canAssignExternalRoles()) {
            throw NctException(NctExceptionCode.USER_NOT_EXTERNAL, "User be external")
        }

        progressProcess.addAdditionalOwners(
            propertyId = id,
            businessId = propertyBusiness.getUUID(),
            userIds = setOf(businessContactUser.id),
            actor = SecurityUtils.getCurrentUser()
        )

        if (simpleUpdateUserRequest != null) {
            when (val outcome = updateUserDetails.update(
                UpdateUserDetails.Input(
                    userId = businessContact.getUserId(),
                    updateUserRequest = simpleUpdateUserRequest,
                    organisation = businessContact.organisation(),
                    businessId = businessContact.getBusinessIdAsUUID(),
                    actor = SecurityUtils.getCurrentUser(),
                    createdAt = null,
                    workflowName = WorkflowName.setAdditionalOwners,
                    additionalSteps = null,
                )
            )) {
                is Outcome.FailedToUpdated.InvalidEmail -> throw NctException(NctExceptionCode.VALIDATION_FAILED, "Invalid email")
                is Outcome.FailedToUpdated.InvalidPhoneNumber -> throw NctException(NctExceptionCode.VALIDATION_FAILED, "Invalid phone number")
                is Outcome.FailedToUpdated.UserNotFound -> throw NctException(NctExceptionCode.USER_NOT_FOUND)
                is Outcome.FailedToUpdated.BusinessNotFound -> throw NctException(NctExceptionCode.BUSINESS_NOT_FOUND)
                is Outcome.Updated -> outcome.updatedUser
            }
        }

        return getOwnersDto(id)
    }

    @RequestMapping(path = ["/property/{id}/additional-owners/user/{userId}"], method = [RequestMethod.DELETE])
    @PreAuthorize(AclPermissionPresets.PROPERTY_INTERNAL_AGENT_PLUS_ABOVE_INCLUDE_OFI_AGENT)
    fun deleteOwner(@PathVariable id: UUID, @PathVariable userId: UUID): PropertyOwnersResponseDto<CrmIdentityDto> {

        val property = fetchPropertyOrNull(id) ?: throw NctException(NctExceptionCode.PROPERTY_NOT_FOUND)
        val propertyBusiness = property.business ?: throw NctException(NctExceptionCode.BUSINESS_NOT_FOUND)

        val businessContact = when (val outcome = fetchBusinessContactByBusinessIdAndUserId.fetch(userId = userId, businessId = propertyBusiness.getUUID())) {
            is FetchBusinessContactByBusinessIdAndUserId.FetchOutcome.NotFound -> null
            is FetchBusinessContactByBusinessIdAndUserId.FetchOutcome.Found -> outcome.businessContact
        }
        val businessContactUser = businessContact?.user?.get() ?: throw NctException(NctExceptionCode.USER_NOT_FOUND)

        deletePropertyOwnersOfAProperty.deleteOwners(
            input = DeleteOwnersOfAProperty.Input(
                propertyId = id,
                businessId = propertyBusiness.getUUID(),
                ownerUserIds = setOf(businessContactUser.id),
                organisationName = SecurityUtils.getCurrentUser().org,
                agency = property.agency
            )
        )

        return getOwnersDto(id)
    }

    private fun getOwnersDto(propertyId: UUID): PropertyOwnersResponseDto<CrmIdentityDto> {
        val relations = when (val fetchOwnersOutcome = fetchPropertyContactRelationships.fetch(propertyId, PropertyRelationContactTag.OWNER)) {
            is FetchPropertyContactRelationships.Outcome.Found -> fetchOwnersOutcome.propertyRelationContactModel.relations
            FetchPropertyContactRelationships.Outcome.NotFound -> return PropertyOwnersResponseDto(emptyList())
        }
        val businessContacts = fetchBusinessContactsByUserIdsAndBusinessIds.fetch(
            relations.map { BusinessContactByUserAndBusiness(userId = it.userId, businessId = it.businessId) }
        )

        return businessContacts
            .mapNotNull { businessContact ->
                val user = businessContact.user?.get()
                user?.let {
                    CrmIdentityDto(
                        user = user,
                        businessId = UUID.fromString(businessContact.businessId),
                        vaultExternalIds = listOf(),
                        canSeeSmsFeature = false,
                        buyerModuleEnabled = false,
                        businessSmsEnabled = false,
                        activePipeBulkEmailEnabled = false,
                        openDatabaseEnabled = false,
                        openBuyerDatabaseEnabled = false,
                        hazardsAndRisksEnabled = false,
                        smsRepliesEnabled = false,
                        acceptedTermsAndConditions = false
                    )
                }
            }
            .sortedBy { e -> e.crmFirstName.lowercase(Locale.getDefault()) }
            .let { owners -> PropertyOwnersResponseDto(owners = owners) }
    }

    private fun fetchPropertyOrNull(propertyId: UUID) =
        fetchPropertyById.fetch(propertyId).let { when (it) {
            is FetchPropertyById.FetchOutcome.NotFound -> null
            is FetchPropertyById.FetchOutcome.Found -> it.property
        } }

}
