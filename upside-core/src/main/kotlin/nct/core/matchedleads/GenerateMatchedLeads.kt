package nct.core.matchedleads

import com.googlecode.objectify.ObjectifyService.ofy
import com.nurturecloud.common.exception.NctException
import com.nurturecloud.common.exception.NctExceptionCode
import nct.core.matchedleads.GenerateMatchedLeads.Companion.DEFERRED_SAVE_CHUNK_SIZE
import nct.core.matchedleads.adapters.datastore.MatchedLeadsRepository
import nct.core.matchedleads.model.MatchRelevance
import nct.core.matchedleads.model.MatchedLeadsModel
import nct.core.matchedleads.model.MatchedProperty
import nct.core.matchedleads.model.toMatchedLeadsModel
import nct.external.domain.SuburbStatsService
import nct.external.domain.models.suburbs.ListingType
import nct.external.domain.models.suburbs.PropertyListingMatchLeadsConverter
import nct.market.listing.FetchRecentlyEditedMarketListingsForSale
import nct.market.listing.FetchTopSoldMarketListingsBySuburbAndOrg
import org.springframework.stereotype.Service
import upside.model.HasOrganisationUtils.organisation
import upside.model.agent.Agent
import upside.model.location.Suburb
import upside.model.property.Property
import upside.process.property.functions.jobs.JobContainer
import upside.service.etl.EtlUtils.etlStreamMatchedLead
import upside.service.tasks.JobQueue

/**
 * Used in volume icr flow to display leads that match a recently sold property.
 */
@Service
class GenerateMatchedLeads(
    private val leadGradingService: LeadGradingService,
    private val suburbStatsService: SuburbStatsService,
    private val matchedLeadsRepository: MatchedLeadsRepository,
    private val jobQueue: JobQueue,
    private val fetchAgentsRecentLeadsBySuburb: FetchAgentsRecentLeadsBySuburb,
    private val matchedLeadsFilter: MatchedLeadsFilter,
    private val generateInternalMatchedLeads: GenerateInternalMatchedLeads,
    private val propertyListingMatchLeadsConverter: PropertyListingMatchLeadsConverter,
    private val fetchRecentlyEditedMarketListingsForSale: FetchRecentlyEditedMarketListingsForSale,
    private val fetchTopSoldMarketListingsBySuburbAndOrg: FetchTopSoldMarketListingsBySuburbAndOrg,
)  {

    object Companion {
        const val MINIMUM_HIGH_MATCHES = 1
        const val MAX_INTERNAL_LEADS_PROCESS_SIZE = 100
        const val DEFERRED_SAVE_CHUNK_SIZE = 30
    }

    /**
     * Given an agent and a suburb, find recent sales and then find matched leads for each of them
     */
    fun storeMatchedLeadsForSuburb(suburb: Suburb, agent: Agent, leadsSource: MatchedLeadsModel.MatchedLeadsSource): Boolean {
        val suburbStats = suburbStatsService.getSuburb(suburb)

        val saleListings = when (leadsSource) {
            MatchedLeadsModel.MatchedLeadsSource.DOMAIN, MatchedLeadsModel.MatchedLeadsSource.REINZ, MatchedLeadsModel.MatchedLeadsSource.TRADE_ME, MatchedLeadsModel.MatchedLeadsSource.RECONZ ->
                suburbStats.saleListings?.map { it.normalise().listing?.let { listing -> propertyListingMatchLeadsConverter.toMatchLeadsModelDirect(listing, agent, suburb) } } ?: emptyList()
            MatchedLeadsModel.MatchedLeadsSource.CORELOGIC -> throw NctException(NctExceptionCode.EXTERNAL_API_SYNC_DISABLED)
            MatchedLeadsModel.MatchedLeadsSource.INTERNAL -> generateInternalMatchedLeads(agent, suburb, ListingType.Sale)
        }.filterNotNull()

        val soldListings = when (leadsSource) {
            MatchedLeadsModel.MatchedLeadsSource.DOMAIN, MatchedLeadsModel.MatchedLeadsSource.REINZ, MatchedLeadsModel.MatchedLeadsSource.TRADE_ME, MatchedLeadsModel.MatchedLeadsSource.RECONZ ->
                suburbStats.soldListings?.map { it.normalise().listing?.let { listing -> propertyListingMatchLeadsConverter.toMatchLeadsModelDirect(listing, agent, suburb) } } ?: emptyList()
            MatchedLeadsModel.MatchedLeadsSource.CORELOGIC -> throw NctException(NctExceptionCode.EXTERNAL_API_SYNC_DISABLED)
            MatchedLeadsModel.MatchedLeadsSource.INTERNAL -> generateInternalMatchedLeads(agent, suburb, ListingType.Sold)
        }.filterNotNull()

        writeLeads(agent, suburb, soldListings, saleListings)

        storeMatchedLeadsForSuburbUsingMarketListings(suburb, agent)

        return true
    }

    private fun generateInternalMatchedLeads(
        agent: Agent,
        suburb: Suburb,
        listingType: ListingType,
    ): List<MatchedLeadsModel> = when (
        val outcome = generateInternalMatchedLeads.generate(
            GenerateInternalMatchedLeads.GenerateInput(
                agent = agent,
                suburb = suburb,
                listingType = listingType,
            ),
        )
    ) {
        is GenerateInternalMatchedLeads.GenerateOutcome.Generated -> outcome.matchedLeads
        is GenerateInternalMatchedLeads.GenerateOutcome.NotGenerated -> listOf()
    }

    private fun deferredStoreMatchedLeadsForDomainListing(
        matchedLead: MatchedLeadsModel,
        leadsInSuburb: List<Property>,
        listingType: ListingType,
        jobContainer: JobContainer
    ) {

        val leadsBeforeGrading = matchedLeadsFilter.filter(leads = leadsInSuburb)

        val gradedLeads = leadGradingService.process(
            matchedLeadsAndListingPropertyDetails = matchedLead.matchedLeadsAndListingPropertyDetails,
            ungradedLeads = leadsBeforeGrading
        )

        if (gradedLeads.isEligibleToBeShown(matchedLead.listingSource)) {
            val lead = matchedLead.apply { addGradedLeadsAndListingType(gradedLeads, listingType) }
            matchedLeadsRepository.deferredPut(lead)
            jobContainer.queue(etlStreamMatchedLead(lead))
        }
    }

    private fun Map<MatchRelevance, List<MatchedProperty>>.isEligibleToBeShown(listingSource: MatchedLeadsModel.MatchedLeadsSource) =
        this.getOrDefault(MatchRelevance.HIGH, listOf()).size >= Companion.MINIMUM_HIGH_MATCHES
            || (listingSource == MatchedLeadsModel.MatchedLeadsSource.INTERNAL && this.values.flatten().isNotEmpty())

    /**
     * Stores matched leads for a suburb using MarketListings from FetchRecentlyEditedMarketListingsForSale and FetchTopSoldMarketListingsBySuburbAndOrg
     */
    private fun storeMatchedLeadsForSuburbUsingMarketListings(suburb: Suburb, agent: Agent): Boolean {
        val organisation = agent.organisation()
        val businessId = agent.defaultAgency?.getBusinessUUID()
        val organisationName = organisation.name

        val saleListings = fetchRecentlyEditedMarketListingsForSale.fetch(
            suburb = suburb,
            organisationName = organisationName,
            businessId = businessId
        ).map { marketListing ->
            marketListing.toMatchedLeadsModel(organisation, agent.idString)
        }

        val soldListings = when (val outcome = fetchTopSoldMarketListingsBySuburbAndOrg.fetch(
            FetchTopSoldMarketListingsBySuburbAndOrg.FetchInput(
                suburbId = suburb.getUuid(),
                organisationName = organisationName
            )
        )) {
            is FetchTopSoldMarketListingsBySuburbAndOrg.FetchOutcome.Found -> outcome.marketListings.map { marketListing ->
                marketListing.toMatchedLeadsModel(organisation, agent.idString)
            }
            is FetchTopSoldMarketListingsBySuburbAndOrg.FetchOutcome.NotFound -> emptyList()
        }

        writeLeads(agent, suburb, soldListings, saleListings)
        return true
    }

    private fun writeLeads(
        agent: Agent,
        suburb: Suburb,
        soldListings: List<MatchedLeadsModel>,
        saleListings: List<MatchedLeadsModel>
    ) {
        val leadsInSuburb = fetchAgentsRecentLeadsBySuburb.fetch(
            FetchAgentsRecentLeadsBySuburbInput(
                agent = agent,
                suburbId = suburb.getId(),
            ),
        ).leads

        val jobContainer = JobContainer()

        soldListings.chunked(DEFERRED_SAVE_CHUNK_SIZE).forEach { chunk ->
            chunk.forEach {
                if (it.matchedLeadsAndListingPropertyDetails.soldDate == null) return@forEach
                deferredStoreMatchedLeadsForDomainListing(it, leadsInSuburb, ListingType.Sold, jobContainer)
            }
            ofy().flush()
        }

        saleListings.chunked(DEFERRED_SAVE_CHUNK_SIZE).forEach { chunk ->
            chunk.forEach {
                deferredStoreMatchedLeadsForDomainListing(it, leadsInSuburb, ListingType.Sale, jobContainer)
            }
            ofy().flush()
        }

        jobQueue.nonTransactionallyEnqueue(jobContainer.jobs)
    }
}
