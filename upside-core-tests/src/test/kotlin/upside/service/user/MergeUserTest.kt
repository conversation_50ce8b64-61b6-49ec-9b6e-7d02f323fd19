package upside.service.user

import com.googlecode.objectify.ObjectifyService.ofy
import com.googlecode.objectify.Ref
import com.ninjasquad.springmockk.MockkBean
import com.nurturecloud.common.type.Either
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.verify
import nct.beholder.contracts.FetchByContactAndTypeSearchFilterResponse
import nct.core.appointment.attendee.adapters.sql.AppointmentAttendeeEntityRepository
import nct.core.businesscontact.FetchBusinessContactsByUserIdsAndBusinessId
import nct.core.buyerrequirements.adapters.client.BeholderBuyerRequirementClient
import nct.core.buyerrequirements.adapters.client.BeholderGracefulClientRequestOutcome
import nct.core.buyerrequirements.adapters.sql.BuyerRequirementRepository
import nct.core.buyerrequirements.model.BuyerRequirement
import nct.core.buyerrequirements.model.BuyerRequirementSuburbs
import nct.core.buyerrequirements.model.Type
import nct.core.buyerrequirements.model.Type.Auto.Companion.ALL_AUTO_ACTIVITY_TYPES
import nct.core.merge.user.EnqueueMergeContractOfSaleRequests
import nct.core.merge.user.adapters.jobs.MergeAppointmentAttendeesJobController
import nct.core.merge.user.adapters.jobs.MergeMeetingsJobController
import nct.core.merge.user.adapters.jobs.MergeRemindersJobController
import nct.core.merge.user.adapters.jobs.MergeUserOffersPayload
import nct.core.merge.user.adapters.jobs.models.MERGE_APPOINTMENT_ATTENDEES_DESTINATION
import nct.core.merge.user.adapters.jobs.models.MERGE_MEETINGS_DESTINATION
import nct.core.merge.user.adapters.jobs.models.MERGE_REMINDERS_DESTINATION
import nct.core.merge.user.adapters.jobs.models.MergeAppointmentAttendeesTask
import nct.core.merge.user.adapters.jobs.models.MergeBuyerPreferencesTask
import nct.core.merge.user.adapters.jobs.models.MergeMeetingsTask
import nct.core.merge.user.adapters.jobs.models.MergeRemindersTask
import nct.core.notes.usermerge.adapters.jobs.NotesUserMergeJobController.Companion.MERGE_USER_NOTES_JOB
import nct.core.property.interestedbuyers.adapters.jobs.InterestedBuyersJobController
import nct.core.property.owners.adapters.web.OwnerUsersJobController
import nct.core.user.merge.adapters.web.MergeInteractionsJobController
import nct.core.user.merge.adapters.web.MergeInteractionsTask
import nct.core.user.merge.adapters.web.MergeMeetingAttendeesJobController
import nct.core.user.merge.adapters.web.MergeMeetingAttendeesTask
import nct.search.service.FullTextSearchReplicationService.Companion.replicateUserForSearch
import org.joda.time.DateTimeUtils
import org.joda.time.Duration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.Transactional
import upside.controllers.dto.response.user.MergeUserValidationResponseDto
import upside.core.location.entity.IntlAddressEntity
import upside.core.location.entity.fromEntity
import upside.model.HasOrganisationUtils.organisation
import upside.model.agent.Agent
import upside.model.business.Business
import upside.model.business.BusinessContact
import upside.model.business.BusinessContactFieldString
import upside.model.business.BusinessContactOtherFieldNames
import upside.model.business.BusinessContactTag
import upside.model.business.TaggedIntlAddress
import upside.model.buyer.Buyer
import upside.model.buyer.BuyerCategory
import upside.model.buyer.BuyerClassification
import upside.model.lead.CreateAgentLeadByUserId
import upside.model.property.Property
import upside.model.property.appointments.AppointmentType
import upside.model.property.attributes.PropertyIntAttributes
import upside.model.property.offers.Offer
import upside.model.property.offers.Offers
import upside.model.property.reminders.ReminderType
import upside.model.request.user.MergeUserRequest
import upside.model.user.MergeUserLog
import upside.model.user.User
import upside.model.user.UserMergeInformation
import upside.model.vendor.Vendor
import upside.process.property.functions.jobs.JobContainer
import upside.process.user.functions.user.UserMergedTo
import upside.repository.ReminderRepository
import upside.service.property.merge.PropertyMergeService.Companion.mergeDuplicateUserPropertiesJob
import upside.service.property.merge.PropertyMergeService.Companion.mergeNoAddressPropertiesJob
import upside.service.spark.SparkJobService.Companion.dismissByUserAndBusiness
import upside.service.tasks.Job
import upside.service.user.merge.ExternalCrmLinkMergeStrategy
import upside.service.user.merge.MergeUserService
import upside.spring.BasePropertyApiTest
import upside.types.internationalisation.CountryEnumeration
import upside.types.organisation.org.LocalDev
import upside.types.property.classifications.CampaignStatus
import upside.types.property.classifications.ContractType
import upside.types.property.classifications.LeadScore
import upside.types.property.classifications.PropertyClassification
import upside.types.property.classifications.PropertyType
import upside.types.spark.SparkDismissedReason
import upside.utils.BusinessContactEmailUtils
import upside.utils.captureQueuedJobs
import upside.utils.fail
import upside.utils.mixins.createMeeting
import upside.utils.objectify.Refs
import upside.utils.reload
import upside.utils.save
import upside.utils.shouldContainOneItemWhere
import upside.utils.success
import upside.vault.service.VaultJobService
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.*

@AutoConfigureWireMock(port = 0, stubs = ["classpath:/mappings/addressFinder"])
@Transactional
@Import(
    MergeInteractionsJobController::class,
    MergeMeetingAttendeesJobController::class,
    MergeAppointmentAttendeesJobController::class,
    MergeMeetingsJobController::class,
    MergeRemindersJobController::class,
    OwnerUsersJobController::class
)
class MergeUserTest(
    @Autowired val mergeUserService: MergeUserService,
    @Autowired private val buyerRequirementRepository: BuyerRequirementRepository,
    @Autowired private val fetchBusinessContactsByUserIdsAndBusinessId: FetchBusinessContactsByUserIdsAndBusinessId,
    @Autowired private val vaultJobService: VaultJobService,
) : BasePropertyApiTest() {
    @MockkBean lateinit var interestedBuyersJobController: InterestedBuyersJobController

    @Autowired
    lateinit var appointmentAttendeeEntityRepository: AppointmentAttendeeEntityRepository

    @MockkBean
    lateinit var enqueueMergeContractOfSaleRequests: EnqueueMergeContractOfSaleRequests

    @MockkBean
    lateinit var beholderBuyerRequirementsClient: BeholderBuyerRequirementClient

    @BeforeEach
    fun beforeEach() {
        appointmentAttendeeEntityRepository.deleteAll()

        every { enqueueMergeContractOfSaleRequests.enqueue(any(), any()) } returns Unit
        every { beholderBuyerRequirementsClient.fetchByBusinessContactAndTypeSearchFilter(any()) } returns BeholderGracefulClientRequestOutcome.Success(FetchByContactAndTypeSearchFilterResponse(emptyList()))
        every { interestedBuyersJobController.enqueueMergeInterestedBuyerJobs(any(), any(), any(), any(), any(), any()) } returns Unit
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    private enum class ExternalUserType {
        Buyer, Vendor, BuyerAndVendor
    }

    private data class UserCreationParameters(
        val number: Int,
        val businesses: List<Business>,
        val externalUserType: ExternalUserType,
        val isShadow: Boolean,
        val isPlatform: Boolean = false,
    )

    fun buildBuyer(number: Int, actor: User): Buyer = fixtureFactoryAu.buildBuyer(firstName = "first$number", lastName = "last$number", email = "custom.user.$<EMAIL>", number = "$number", areaCode = "$number", creatorUser = actor)
    fun buildVendor(number: Int, actor: User): Vendor = fixtureFactoryAu.buildVendor(firstName = "first$number", lastName = "last$number", email = "custom.user.$<EMAIL>", phoneNumber = "$number", areaCode = "$number", creatorUser = actor)
    fun buildBuyerAndVendor(number: Int, actor: User): Triple<Buyer, Vendor, User> = fixtureFactoryAu.buildBuyerAndVendor(firstName = "first$number", lastName = "last$number", email = "custom.user.$<EMAIL>", phoneNumber = "$number", areaCode = "$number", creatorUser = actor)
    fun businessActorEmail(business: Business): String = "actor@${business.id}.com"
    fun businessActor(business: Business): User = businessActorEmail(business).let { email ->
        userProcess.getByEmail(organisation, email) ?: fixtureFactoryAu.buildSalesAdmin(email = email, business = business)
    }

    fun defaultMerge(primaryId: UUID, userIds: List<UUID>): Either<MergeUserValidationResponseDto, MergeUserLog> = mergeUserService.merge(
        request = MergeUserRequest(
            businessId = defaultBusiness.getUUID(),
            primaryUserId = primaryId,
            userIds = userIds,
            null, null, null, null, null, null
        ),
        dryRun = false,
        actor = salesAdminUser,
        mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
    )

    private fun createUser(parameters: UserCreationParameters): User {
        val actor = admin
        val user = when (parameters.externalUserType) {
            ExternalUserType.Buyer -> buildBuyer(parameters.number, actor).user
            ExternalUserType.Vendor -> buildVendor(parameters.number, actor).user
            ExternalUserType.BuyerAndVendor -> buildBuyerAndVendor(parameters.number, actor).third
        }
        val ret = user.save {
            this.shadow = parameters.isShadow
            this.agentEditable = !parameters.isPlatform
        }
        parameters.businesses.forEach { business ->
            userProgress.createBusinessContactFromPlatform(user, businessActor(business))
            val bc =fetchBusinessContactsByUserIdsAndBusinessId.fetch(listOf(user.id), business.getUUID()).businessContacts[0].shouldNotBeNull()
            ALL_AUTO_ACTIVITY_TYPES.forEach { activityType ->
                buyerRequirementRepository.insert(
                    business.getUUID(),
                    BuyerRequirement(
                        id = UUID.randomUUID(),
                        orgName = user.organisation().name,
                        userId = user.id,
                        businessId = business.getUUID(),
                        purpose = BuyerCategory.Uncategorized,
                        type = Type.Auto(activityType),
                        minBedroom = null,
                        minBathroom = null,
                        minGarage = null,
                        budget = null,
                        landArea = null,
                        floorArea = null,
                        propertyTypes = emptySet(),
                        suburbs = BuyerRequirementSuburbs.NoSuburbs,
                        contractType = ContractType.Sale,
                        correlationId = "",
                        lastTouched = OffsetDateTime.now(),
                        contactId = bc.getContactId(),
                    )
                )
            }
        }

        return ret
    }

    private fun getAllJobsIncludingFanout(): List<Job> {
        val capturedJobs = jobQueue.captureQueuedJobs()
        val fanoutJobs = capturedJobs.filter { it.destination == "fanout" && it.payload is JobContainer }
        val jobsInFanout = fanoutJobs.flatMap { (it.payload as JobContainer).jobs }
        return capturedJobs + jobsInFanout
    }

    private fun addProperty(
        user: User,
        address: IntlAddressEntity,
        numBedrooms: Int = 3,
        agent: Agent = agent1,
        actor: User = salesManagerNswUser1,
        classification: PropertyClassification = PropertyClassification.Residential,
    ): Property = leadProcess.create(
        model = CreateAgentLeadByUserId(
            userId = user.id,
            leadScore = LeadScore.values().random(),
            workflowReminderCadence = null,
            address = address.fromEntity(),
            vendorMedium = null,
            note = null,
            isAppraisal = true,
            classification = classification,
            sendPropertyReport = false,
        ),
        user = user,
        agent = agent,
        actor = actor,
        source = "source",
        vendorLastContacted = null,
        updateVendor = false,
        shouldExportToExternalCrm = true,
    )
        .save {
            type = PropertyType.House
            setAgency(agent.defaultAgencyRef)
            setAttribute(PropertyIntAttributes.BEDROOMS, numBedrooms)
            this.addPortalSyncOrgDefaults()
        }
        .also { property ->
            val updatedReminders = ReminderRepository
                .getPropertyReminders(Refs.ref(property), 100)
                .map { reminder ->
                    when (reminder.type) {
                        ReminderType.OrganiseAppraisalVisit -> reminder.setAssignedTo(Refs.ref(agentUser1))
                        else -> reminder
                    }
                }
            ReminderRepository.put(updatedReminders)
        }

    private fun address(index: Int) = fixtureFactoryAu.address(streetNumber = "1", unitNumber = "$index")

    @Test
    fun `any user with not inactive properties must be primary`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        val property1 = addProperty(user1, address(1)).save {
            this.campaignStatus = CampaignStatus.Sold
        }

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())
        val info = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        info.mustBePrimary shouldBe true
        info.primaryReasons.notInactivePropertyIds shouldBe listOf(property1.id.toString())
    }

    @Test
    fun `any user with no business contact must be primary`() {
        val user1 = createUser(UserCreationParameters(1, listOf(), ExternalUserType.Vendor, false))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())
        val info = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        info.mustBePrimary shouldBe true
        info.primaryReasons.hasNoBusinessContact shouldBe true
    }

    @Test
    fun `a list of shadow users can be merged`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, true))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, true))

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())
        val info1 = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info2 = (infoMap[user2.id].success() as? UserMergeInformation.CanBeMerged).success()
        info1.mustBeSecondary shouldBe false
        info2.mustBeSecondary shouldBe false
    }

    @Test
    fun `a list of non-shadow users can be merged`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())
        val info1 = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info2 = (infoMap[user2.id].success() as? UserMergeInformation.CanBeMerged).success()
        info1.mustBeSecondary shouldBe false
        info2.mustBeSecondary shouldBe false
    }

    @Test
    fun `any shadow user in a list including non-shadow users must be secondary`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, true))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, true))
        val user3 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val user4 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2, user3, user4), defaultBusiness.getUUID())
        val info1 = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info2 = (infoMap[user2.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info3 = (infoMap[user3.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info4 = (infoMap[user4.id].success() as? UserMergeInformation.CanBeMerged).success()
        info1.mustBeSecondary shouldBe true
        info1.secondaryReasons.isShadowButOthersAreNot shouldBe true
        info2.mustBeSecondary shouldBe true
        info2.secondaryReasons.isShadowButOthersAreNot shouldBe true
        info3.mustBeSecondary shouldBe false
        info4.mustBeSecondary shouldBe false
    }

    @Test
    fun `any user with invalid or no email must be secondary, if there is at least one user with a valid email`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val beautifulButInvalidEmail = "%sd**(¡™•"
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setEmail(beautifulButInvalidEmail)
        }
        val user3 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail(null)
        }
        val user4 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail("")
        }
        val user5 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail("    ")
        }

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2, user3, user4), defaultBusiness.getUUID())
        val info1 = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info2 = (infoMap[user2.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info3 = (infoMap[user3.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info4 = (infoMap[user4.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info5 = (infoMap[user5.id].success() as? UserMergeInformation.CanBeMerged).success()
        info1.mustBeSecondary shouldBe false
        info2.mustBeSecondary shouldBe true
        info2.secondaryReasons.hasNoEmailButOthersDo shouldBe true
        info3.mustBeSecondary shouldBe true
        info3.secondaryReasons.hasNoEmailButOthersDo shouldBe true
        info4.mustBeSecondary shouldBe true
        info4.secondaryReasons.hasNoEmailButOthersDo shouldBe true
        info5.mustBeSecondary shouldBe true
        info5.secondaryReasons.hasNoEmailButOthersDo shouldBe true
    }

    @Test
    fun `any user with a ghost email can be primary, as long as the other emails are invalid`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail(null)
        }
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail(BusinessContactEmailUtils.makeGhostEmail(222L))
        }

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())
        val info1 = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info2 = (infoMap[user2.id].success() as? UserMergeInformation.CanBeMerged).success()
        info1.mustBeSecondary shouldBe false
        info2.mustBeSecondary shouldBe false
    }

    @Test
    fun `if all users have ghost emails, then there is no email based restriction on mergability`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail(BusinessContactEmailUtils.makeGhostEmail(111L))
        }
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail(BusinessContactEmailUtils.makeGhostEmail(222L))
        }

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())
        val info1 = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info2 = (infoMap[user2.id].success() as? UserMergeInformation.CanBeMerged).success()
        info1.mustBeSecondary shouldBe false
        info2.mustBeSecondary shouldBe false
    }

    @Test
    fun `if no users have a valid email, then there's no email-based restriction on mergeability`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setEmail("lalalala")
        }
        val beautifulButInvalidEmail = "%sd**(¡™•"
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setEmail(beautifulButInvalidEmail)
        }
        val user3 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail(null)
        }
        val user4 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail("")
        }
        val user5 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.setNonNormalisedEmail("    ")
        }

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2, user3, user4), defaultBusiness.getUUID())
        val info1 = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info2 = (infoMap[user2.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info3 = (infoMap[user3.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info4 = (infoMap[user4.id].success() as? UserMergeInformation.CanBeMerged).success()
        val info5 = (infoMap[user5.id].success() as? UserMergeInformation.CanBeMerged).success()
        info1.mustBeSecondary shouldBe false
        info2.mustBeSecondary shouldBe false
        info3.mustBeSecondary shouldBe false
        info4.mustBeSecondary shouldBe false
        info5.mustBeSecondary shouldBe false
    }

    @Test
    fun `primary user should be both a buyer and vendor after a merge`() {
        val primary1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val primary2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary2 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        mergeUserService.merge(
            request = MergeUserRequest(
                businessId = defaultBusiness.getUUID(),
                primaryUserId = primary1.id,
                userIds = listOf(primary1.id, secondary1.id),
                null, null, null, null, null, null
            ),
            dryRun = false,
            actor = salesAdminUser,
            mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
        ).success()

        mergeUserService.merge(
            request = MergeUserRequest(
                businessId = defaultBusiness.getUUID(),
                primaryUserId = primary2.id,
                userIds = listOf(primary2.id, secondary2.id),
                null, null, null, null, null, null
            ),
            dryRun = false,
            actor = salesAdminUser,
            mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
        ).success()

        listOf(primary1, primary2).forEach {
            val user = it.reload()
            user.isVendor shouldBe true
            user.isBuyer shouldBe true
            vendorService.get(user.id) shouldNotBe null
            buyerService.get(user.id) shouldNotBe null
        }
    }

    @Test
    fun `secondary business contacts should be marked as deleted`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        mergeUserService.merge(
            request = MergeUserRequest(
                businessId = defaultBusiness.getUUID(),
                primaryUserId = primary.id,
                userIds = listOf(primary.id, secondary1.id, secondary2.id),
                null, secondary1.id, null, null, null, null
            ),
            dryRun = false,
            actor = salesAdminUser,
            mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
        ).success()

        ofy().load().type(BusinessContact::class.java).parent(primary).id(defaultBusiness.id).now().success().deleted shouldBe false
        ofy().load().type(BusinessContact::class.java).parent(secondary1).id(defaultBusiness.id).now().success().deleted shouldBe true
        ofy().load().type(BusinessContact::class.java).parent(secondary2).id(defaultBusiness.id).now().success().deleted shouldBe true
        businessContactRepository.get(primary, defaultBusiness) shouldNotBe null
        businessContactRepository.get(secondary1, defaultBusiness) shouldBe null
        businessContactRepository.get(secondary2, defaultBusiness) shouldBe null
    }

    @Test
    fun `should correctly merge and de-duplicate relevant business contact fields`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary3 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        primary.getBusinessContact(defaultBusiness.getUUID()).success().save {
            this.primaryStructuredIntlAddress = IntlAddressEntity(streetNumber = "1", streetName = "Fake St")
            this.addOtherName("extraFirst2", "extraLast2")
            this.addOtherStructuredAddress(TaggedIntlAddress(BusinessContactTag.Home, IntlAddressEntity(streetNumber = "2", streetName = "Fake St")))
            this.addExtraPhoneNumber("123", null)
            this.addOtherEmail("<EMAIL>", BusinessContactTag.Work)
            // This should be invalid, and ignored
            this.addOtherEmail("&&d9s(", BusinessContactTag.Work)
        }
        secondary1.getBusinessContact(defaultBusiness.getUUID()).success().save {
            this.addOtherName("extraFirst1", "extraLast1")
            this.addOtherStructuredAddress(TaggedIntlAddress(BusinessContactTag.Home, IntlAddressEntity(streetNumber = "1", streetName = "Fake St")))
            this.primaryStructuredIntlAddress = IntlAddressEntity(streetNumber = "2", streetName = "Fake St")
            this.addExtraPhoneNumber("123", BusinessContactTag.Home)
            // This should match the primary user's email, and be deduplicated
            this.addOtherEmail("<EMAIL>", BusinessContactTag.Work)
        }
        secondary2.getBusinessContact(defaultBusiness.getUUID()).success().save {
            this.addOtherName("first1", "last1")
            this.addOtherStructuredAddress(TaggedIntlAddress(BusinessContactTag.Home, IntlAddressEntity(streetNumber = "1", streetName = "Fake St")))
            this.addExtraPhoneNumber("456", BusinessContactTag.Home)
            this.addOtherEmail("<EMAIL>", BusinessContactTag.Work)
        }
        secondary3.getBusinessContact(defaultBusiness.getUUID()).success().save {
            this.addOtherName("extraFirst1", "extraLast1")
            this.crmPhoneNumber = "123"
            this.addOtherEmail("<EMAIL>", BusinessContactTag.Work)
        }

        mergeUserService.merge(
            request = MergeUserRequest(
                businessId = defaultBusiness.getUUID(),
                primaryUserId = primary.id,
                userIds = listOf(primary.id, secondary1.id, secondary2.id, secondary3.id),
                nameUserId = secondary2.id,
                phoneUserId = secondary3.id,
                addressUserId = secondary1.id, null, null, null
            ),
            dryRun = false,
            actor = salesAdminUser,
            mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
        ).success()
        primary.getBusinessContact(defaultBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first3"
            it.crmLastName shouldBe "last3"
            it.otherNames shouldContainExactlyInAnyOrder listOf(
                BusinessContactOtherFieldNames(BusinessContactTag.Name, "extraFirst1", "extraLast1"),
                BusinessContactOtherFieldNames(BusinessContactTag.Name, "extraFirst2", "extraLast2"),
                BusinessContactOtherFieldNames(BusinessContactTag.Name, "first1", "last1"),
                BusinessContactOtherFieldNames(BusinessContactTag.Name, "first2", "last2"),
                BusinessContactOtherFieldNames(BusinessContactTag.Name, "first4", "last4"),
            )
            it.primaryStructuredIntlAddress shouldBe IntlAddressEntity(streetNumber = "2", streetName = "Fake St", countryIdentifier = CountryEnumeration.UNKNOWN)
            // The 2 Fake St entries should be de-duped, as it is the Primary Address
            it.otherStructuredIntlAddresses shouldContainExactlyInAnyOrder listOf(
                TaggedIntlAddress(BusinessContactTag.Home, IntlAddressEntity(streetNumber = "1", streetName = "Fake St"))
            )

            it.otherEmails shouldContainExactlyInAnyOrder listOf(
                BusinessContactFieldString(BusinessContactTag.Work, "<EMAIL>"),
                BusinessContactFieldString(BusinessContactTag.Work, "<EMAIL>"),
                BusinessContactFieldString(null, "<EMAIL>"),
                BusinessContactFieldString(null, "<EMAIL>"),
                BusinessContactFieldString(null, "<EMAIL>"),
            )

            // User 3 (secondary2) should give its crmPhoneNumber
            it.crmPhoneNumber shouldBe "123"
            it.otherPhoneNumbers shouldContainExactlyInAnyOrder listOf(
                BusinessContactFieldString(BusinessContactTag.Home, "456"),
                BusinessContactFieldString(null, "1"),
                BusinessContactFieldString(null, "2"),
                BusinessContactFieldString(null, "3"),
            )
        }
    }

    @Test
    fun `should merge correctly into a user created from another business`() {
        val anotherBusiness = fixtureFactoryAu.anotherBusiness
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(anotherBusiness), ExternalUserType.Vendor, false))

        primary.getBusinessContact(defaultBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first1"
            it.crmLastName shouldBe "last1"
            it.crmPhoneNumber shouldBe "1"
            it.crmPhoneNumberAreaCode shouldBe "1"
        }
        primary.hasBusinessContact(anotherBusiness.getUUID()) shouldBe false

        secondary1.hasBusinessContact(defaultBusiness.getUUID()) shouldBe false
        secondary1.getBusinessContact(anotherBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first2"
            it.crmLastName shouldBe "last2"
            it.crmPhoneNumber shouldBe "2"
            it.crmPhoneNumberAreaCode shouldBe "2"
        }

        mergeUserService.merge(
            request = MergeUserRequest(
                businessId = anotherBusiness.getUUID(),
                primaryUserId = primary.id,
                userIds = listOf(primary.id, secondary1.id),
                nameUserId = secondary1.id,
                phoneUserId = secondary1.id,
                addressUserId = secondary1.id, null, null, null
            ),
            dryRun = false,
            actor = salesAdminUser,
            mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
        ).success()

        primary.getBusinessContact(defaultBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first1"
            it.crmLastName shouldBe "last1"
            it.crmPhoneNumber shouldBe "1"
            it.crmPhoneNumberAreaCode shouldBe "1"
        }

        primary.getBusinessContact(anotherBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first2"
            it.crmLastName shouldBe "last2"
            it.crmPhoneNumber shouldBe "2"
            it.crmPhoneNumberAreaCode shouldBe "2"
        }

        secondary1.hasBusinessContact(defaultBusiness.getUUID()) shouldBe false
        secondary1.hasBusinessContact(anotherBusiness.getUUID()) shouldBe false
    }

    @Test
    fun `should split off a business contact from an existing user`() {
        val anotherBusiness = fixtureFactoryAu.anotherBusiness
        val primary = createUser(UserCreationParameters(1, listOf(), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness, anotherBusiness), ExternalUserType.Vendor, false))

        primary.hasBusinessContact(defaultBusiness.getUUID()) shouldBe false
        primary.hasBusinessContact(anotherBusiness.getUUID()) shouldBe false

        secondary1.getBusinessContact(defaultBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first2"
            it.crmLastName shouldBe "last2"
            it.crmPhoneNumber shouldBe "2"
            it.crmPhoneNumberAreaCode shouldBe "2"
        }
        secondary1.getBusinessContact(anotherBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first2"
            it.crmLastName shouldBe "last2"
            it.crmPhoneNumber shouldBe "2"
            it.crmPhoneNumberAreaCode shouldBe "2"
        }

        mergeUserService.merge(
            request = MergeUserRequest(
                businessId = anotherBusiness.getUUID(),
                primaryUserId = primary.id,
                userIds = listOf(primary.id, secondary1.id),
                nameUserId = secondary1.id,
                phoneUserId = secondary1.id,
                addressUserId = secondary1.id, null, null, null
            ),
            dryRun = false,
            actor = salesAdminUser,
            mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
        ).success()

        primary.hasBusinessContact(defaultBusiness.getUUID()) shouldBe false
        primary.getBusinessContact(anotherBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first2"
            it.crmLastName shouldBe "last2"
            it.crmPhoneNumber shouldBe "2"
            it.crmPhoneNumberAreaCode shouldBe "2"
        }

        secondary1.getBusinessContact(defaultBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "first2"
            it.crmLastName shouldBe "last2"
            it.crmPhoneNumber shouldBe "2"
            it.crmPhoneNumberAreaCode shouldBe "2"
        }
        secondary1.hasBusinessContact(anotherBusiness.getUUID()) shouldBe false
    }

    @Test
    fun `should correctly merge and de-duplicate other names fields`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        primary.getBusinessContact(defaultBusiness.getUUID()).success().save {
            this.crmFirstName = "Amy"
            this.crmLastName = "Wu"
            this.addOtherName("M", "m", null)
        }
        secondary1.getBusinessContact(defaultBusiness.getUUID()).success().save {
            this.crmFirstName = "Amy"
            this.crmLastName = "W"
            this.addOtherName("John", "Smith", BusinessContactTag.Partner)
        }

        defaultMerge(primaryId = primary.id, userIds = listOf(primary.id, secondary1.id)).success()

        primary.getBusinessContact(defaultBusiness.getUUID()).success().let {
            it.crmFirstName shouldBe "Amy"
            it.crmLastName shouldBe "Wu"
            it.otherNames shouldContainExactlyInAnyOrder listOf(
                BusinessContactOtherFieldNames(BusinessContactTag.Name, "Amy", "W"),
                BusinessContactOtherFieldNames(null, "M", "m"),
                BusinessContactOtherFieldNames(BusinessContactTag.Partner, "John", "Smith"),
            )
        }
    }

    @Test
    fun `should correctly enqueue buyer preferences merge task`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        val expectedPayload = MergeBuyerPreferencesTask(
            primaryUserId = primary.id,
            secondaryUserIds = listOf(secondary1.id, secondary2.id),
            businessId = defaultBusiness.getUUID(),
        )

        val allJobs = getAllJobsIncludingFanout()
        val hasBuyerPrefJob = allJobs.any { it.queue == "merge-event" && it.payload == expectedPayload }
        hasBuyerPrefJob shouldBe true
    }

    @Test
    fun `should correctly enqueue merge interactions`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        resetJobQueue()
        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        val allJobs = getAllJobsIncludingFanout()
        val mergeInteractionJobs = allJobs
            .filter { it.destination == MergeInteractionsTask.DESTINATION }
            .mapNotNull { it.payload as? MergeInteractionsTask }

        mergeInteractionJobs.size shouldBe 2

        val taskUserIds = mergeInteractionJobs.map { it.secondaryUserId }.toSet()
        taskUserIds shouldContainExactlyInAnyOrder setOf(secondary1.id, secondary2.id)

        mergeInteractionJobs.all { it.primaryUserId == primary.id } shouldBe true
    }

    @Test
    fun `should queue job to merge notes`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        val allJobs = getAllJobsIncludingFanout()
        val mergeNotesJobs = allJobs.filter { it.destination == MERGE_USER_NOTES_JOB }
        mergeNotesJobs.size shouldBe 2
    }

    @Test
    fun `should correctly enqueue merge reminders`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        resetJobQueue()
        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        val allJobs = getAllJobsIncludingFanout()
        val mergeReminderJobs = allJobs
            .filter { it.destination == MERGE_REMINDERS_DESTINATION }
            .mapNotNull { it.payload as? MergeRemindersTask }

        mergeReminderJobs.size shouldBe 2

        val taskUserIds = mergeReminderJobs.map { it.secondaryUserId }.toSet()
        taskUserIds shouldContainExactlyInAnyOrder setOf(secondary1.id, secondary2.id)

        mergeReminderJobs.all { it.primaryUserId == primary.id } shouldBe true
    }

    @Test
    fun `should correctly enqueue merge meetings`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        resetJobQueue()
        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        val allJobs = getAllJobsIncludingFanout()
        val mergeMeetingsJobs = allJobs
            .filter { it.destination == MERGE_MEETINGS_DESTINATION }
            .mapNotNull { it.payload as? MergeMeetingsTask }

        mergeMeetingsJobs.size shouldBe 2

        val taskUserIds = mergeMeetingsJobs.map { it.secondaryUserId }.toSet()
        taskUserIds shouldContainExactlyInAnyOrder setOf(secondary1.id, secondary2.id)

        mergeMeetingsJobs.all { it.primaryUserId == primary.id } shouldBe true
    }

    @Test
    fun `should correctly merge vendor properties when vault sync isn't enabled`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val property1 = addProperty(primary, address(1))
        val property2 = addProperty(secondary1, address(2))
        val property3 = addProperty(secondary2, address(3))

        primary.reload()
        secondary1.reload()
        secondary2.reload()

        // Check pre-merge assumptions
        property1.vendor shouldBe primary.vendor.success()
        property2.vendor shouldBe secondary1.vendor.success()
        property3.vendor shouldBe secondary2.vendor.success()

        resetJobQueue()

        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        property1.reload().vendor shouldBe primary.vendor.success()
        property2.reload().vendor shouldBe primary.vendor.success()
        property3.reload().vendor shouldBe primary.vendor.success()

        propertyService.getVendorProperties(primary).map { it.id } shouldContainExactlyInAnyOrder listOf(property1.id, property2.id, property3.id)

        val allJobs = getAllJobsIncludingFanout()
        allJobs.containsAll(
            listOf(
                dismissByUserAndBusiness(secondary1, defaultBusiness, salesAdminUser, SparkDismissedReason.UserMerged),
                dismissByUserAndBusiness(secondary2, defaultBusiness, salesAdminUser, SparkDismissedReason.UserMerged),
                replicateUserForSearch(Ref.create(primary)),
                replicateUserForSearch(Ref.create(secondary1)),
                replicateUserForSearch(Ref.create(secondary2)),
                mergeDuplicateUserPropertiesJob(primary),
                mergeNoAddressPropertiesJob(primary)
            )
        ) shouldBe true
        unMockDefaultOrg()
    }

    @Test
    fun `if vault sync is enabled, should re-import properties not merge`() {
        mockVaultSyncEnabledEnvironment()

        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val property1 = addProperty(primary, address(1))
        val property2 = addProperty(secondary1, address(2))
        val property3 = addProperty(secondary2, address(3))

        primary.reload()
        secondary1.reload()
        secondary2.reload()

        // Check pre-merge assumptions
        property1.vendor shouldBe primary.vendor.success()
        property2.vendor shouldBe secondary1.vendor.success()
        property3.vendor shouldBe secondary2.vendor.success()

        resetJobQueue()

        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        property1.reload().vendor shouldBe primary.vendor.success()
        property2.reload().vendor shouldBe primary.vendor.success()
        property3.reload().vendor shouldBe primary.vendor.success()

        propertyService.getVendorProperties(primary).map { it.id } shouldContainExactlyInAnyOrder listOf(property1.id, property2.id, property3.id)

        val allJobs2 = getAllJobsIncludingFanout()
        allJobs2.containsAll(
            listOf(
                dismissByUserAndBusiness(secondary1, defaultBusiness, salesAdminUser, SparkDismissedReason.UserMerged),
                dismissByUserAndBusiness(secondary2, defaultBusiness, salesAdminUser, SparkDismissedReason.UserMerged),
                replicateUserForSearch(Ref.create(primary)),
                replicateUserForSearch(Ref.create(secondary1)),
                replicateUserForSearch(Ref.create(secondary2)),
                mergeNoAddressPropertiesJob(primary),
                vaultJobService.constructImportPropertyByBusinessContactJob(primary.id, defaultBusiness.getUUID()),
                vaultJobService.constructImportPropertyByBusinessContactJob(secondary1.id, defaultBusiness.getUUID()),
                vaultJobService.constructImportPropertyByBusinessContactJob(secondary2.id, defaultBusiness.getUUID())
            )
        ) shouldBe true

        val allJobs3 = getAllJobsIncludingFanout()
        allJobs3.contains(mergeDuplicateUserPropertiesJob(primary)) shouldBe false
    }

    @Test
    fun `should correctly merge interested buyers`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val extraUser = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val property1 = addProperty(extraUser, address(1))
        val property2 = addProperty(extraUser, address(2))

        val meeting1 = calendarProcess.createMeeting(property1, agent1, salesManagerNsw1.id, AppointmentType.AGENT_PUBLIC_OPEN, now.plusHours(10), Duration.standardHours(1))
        DateTimeUtils.setCurrentMillisFixed(meeting1.dateTime.millis)
        setupCheckinWithExistingUser(
            property = meeting1.property.get(),
            appointment = meeting1,
            attendeeUser = secondary1,
            actorUser = agentUser1,
            note = "note",
            enquirySource = null,
            buyerClassification = BuyerClassification.UNCLASSIFIED,
            brochureRequested = false,
            locality = null,
        )

        setupCheckinWithExistingUser(
            property = meeting1.property.get(),
            appointment = meeting1,
            attendeeUser = primary,
            actorUser = agentUser1,
            note = "note",
            enquirySource = null,
            buyerClassification = BuyerClassification.UNCLASSIFIED,
            brochureRequested = false,
            locality = null,
        )
        DateTimeUtils.setCurrentMillisSystem()

        val meeting2 = calendarProcess.createMeeting(property2, agent1, salesManagerNsw1.id, AppointmentType.AGENT_PUBLIC_OPEN, now.plusHours(11), Duration.standardHours(1))
        DateTimeUtils.setCurrentMillisFixed(meeting2.dateTime.millis)
        setupCheckinWithExistingUser(
            property = meeting2.property.get(),
            appointment = meeting2,
            attendeeUser = secondary1,
            actorUser = agentUser1,
            note = "note",
            enquirySource = null,
            buyerClassification = BuyerClassification.UNCLASSIFIED,
            brochureRequested = false,
            locality = null,
        )

        setupCheckinWithExistingUser(
            property = meeting2.property.get(),
            appointment = meeting2,
            attendeeUser = secondary2,
            actorUser = agentUser1,
            note = "note",
            enquirySource = null,
            buyerClassification = BuyerClassification.UNCLASSIFIED,
            brochureRequested = false,
            locality = null,
        )
        DateTimeUtils.setCurrentMillisSystem()

        // Check pre-merge assumptions
        interestedBuyersRepository.get(property1.id).success().let {
            it.getBuyers().size shouldBe 2
            val ib1 = it.getInterestedBuyer(secondary1.email).success()
            val ib2 = it.getInterestedBuyer(primary.email).success()
            ib1.buyer().user.idString shouldBe secondary1.idString
            ib1.publicOpensAttended shouldBe 1
            ib1.privateOpensAttended shouldBe 0
            ib2.buyer().user.idString shouldBe primary.idString
            ib2.publicOpensAttended shouldBe 1
            ib2.privateOpensAttended shouldBe 0
        }
        interestedBuyersRepository.get(property2.id).success().let {
            it.getBuyers().size shouldBe 2
            val ib1 = it.getInterestedBuyer(secondary1.email).success()
            val ib2 = it.getInterestedBuyer(secondary2.email).success()
            ib1.buyer().user.idString shouldBe secondary1.idString
            ib1.publicOpensAttended shouldBe 1
            ib1.privateOpensAttended shouldBe 0
            ib2.buyer().user.idString shouldBe secondary2.idString
            ib2.publicOpensAttended shouldBe 1
            ib2.privateOpensAttended shouldBe 0
        }

        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        verify(exactly = 1) {
            interestedBuyersJobController.enqueueMergeInterestedBuyerJobs(
                salesAdminUser.id,
                defaultBusiness.getUUID(),
                primary.id,
                setOf(secondary1.id, secondary2.id),
                false,
                any()
            )
        }

        verify {
            enqueueMergeContractOfSaleRequests.enqueue(
                match {
                    it.primaryUserId == primary.id &&
                        it.secondaryUserIds.containsAll(listOf(secondary1.id, secondary2.id)) &&
                        it.businessId == defaultBusiness.getUUID()
                },
                any()
            )
        }
    }

    @Test
    fun `should correctly enqueue merge meeting attendees`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        resetJobQueue()
        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        val allJobs = getAllJobsIncludingFanout()
        val mergeMeetingAttendeesJobs = allJobs
            .filter { it.destination == MergeMeetingAttendeesTask.DESTINATION }
            .mapNotNull { it.payload as? MergeMeetingAttendeesTask }

        mergeMeetingAttendeesJobs.size shouldBe 2

        val taskUserIds = mergeMeetingAttendeesJobs.map { it.secondaryUserId }.toSet()
        taskUserIds shouldContainExactlyInAnyOrder setOf(secondary1.id, secondary2.id)

        mergeMeetingAttendeesJobs.all { it.primaryUserId == primary.id } shouldBe true
    }

    @Test
    fun `should correctly enqueue merge appointment attendees`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        resetJobQueue()
        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()

        val allJobs = getAllJobsIncludingFanout()
        val mergeAppointmentAttendeesJobs = allJobs
            .filter { it.destination == MERGE_APPOINTMENT_ATTENDEES_DESTINATION }
            .mapNotNull { it.payload as? MergeAppointmentAttendeesTask }

        mergeAppointmentAttendeesJobs.size shouldBe 2

        val taskUserIds = mergeAppointmentAttendeesJobs.map { it.secondaryUserId }.toSet()
        taskUserIds shouldContainExactlyInAnyOrder setOf(secondary1.id, secondary2.id)

        mergeAppointmentAttendeesJobs.all { it.primaryUserId == primary.id } shouldBe true
    }

    @Test
    fun `should correctly merge offers`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val extraBuyerUser = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val extraVendorUser = createUser(UserCreationParameters(5, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val property1 = addProperty(extraVendorUser, address(1))
        val property2 = addProperty(extraVendorUser, address(2))
        val property3 = addProperty(extraVendorUser, address(3))

        offersRepository.put(
            Offers(property1)
                .setOffers(
                    listOf(
                        Offer(primary.buyer.success()).setOfferAmount(BigDecimal.valueOf(100000L)),
                        Offer(extraBuyerUser.buyer.success()).setOfferAmount(BigDecimal.valueOf(200000L)),
                    )
                )
        )
        offersRepository.put(
            Offers(property2)
                .setOffers(
                    listOf(
                        Offer(secondary1.buyer.success()).setOfferAmount(BigDecimal.valueOf(100000L)),
                        Offer(extraBuyerUser.buyer.success()).setOfferAmount(BigDecimal.valueOf(200000L)),
                    )
                )
        )
        offersRepository.put(
            Offers(property3)
                .setOffers(
                    listOf(
                        Offer(secondary2.buyer.success()).setOfferAmount(BigDecimal.valueOf(100000L)),
                    )
                )
        )

        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()
        offersRepository.get(property1.id).success().let {
            it.buyerOffersBuyerIds shouldContainExactlyInAnyOrder listOf(primary.buyer.success().id, extraBuyerUser.buyer.success().id)
            it.buyerOffers.get(primary.buyer.success().id).success().buyer shouldBe primary.buyer
            it.buyerOffers.get(extraBuyerUser.buyer.success().id).success().buyer shouldBe extraBuyerUser.buyer
        }
        offersRepository.get(property2.id).success().let {
            it.buyerOffersBuyerIds shouldContainExactlyInAnyOrder listOf(primary.buyer.success().id, extraBuyerUser.buyer.success().id)
            it.buyerOffers.get(primary.buyer.success().id).success().buyer shouldBe primary.buyer
            it.buyerOffers.get(extraBuyerUser.buyer.success().id).success().buyer shouldBe extraBuyerUser.buyer
        }
        offersRepository.get(property3.id).success().let {
            it.buyerOffersBuyerIds shouldContainExactlyInAnyOrder listOf(primary.buyer.success().id)
            it.buyerOffers.get(primary.buyer.success().id).success().buyer shouldBe primary.buyer
        }
    }

    @Test
    fun `should correctly merge offers v3`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val extraBuyerUser = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val extraVendorUser = createUser(UserCreationParameters(5, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val property1 = addProperty(extraVendorUser, address(1))
        val property2 = addProperty(extraVendorUser, address(2))
        val property3 = addProperty(extraVendorUser, address(3))


        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).success()
        val allJobs = getAllJobsIncludingFanout()
        val hasOffersJob = allJobs.any { job ->
            job.queue == "merge-event" &&
                job.payload == MergeUserOffersPayload(
                    primary.id,
                    setOf(secondary1.id, secondary2.id),
                    defaultBusiness.getUUID()
                )
        }
        hasOffersJob shouldBe true

    }

    @Test
    fun `should correctly create a user merge activity feed item for the primary user`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id))

        val displayActivityGroup = activityService
            .userDisplayActivity(primary, null)
            .first { it.name == "User merged" }

        val displayActivities = displayActivityGroup.activity!!
        displayActivities.size shouldBe 5

        displayActivities shouldContainOneItemWhere {
            it.title == "Other Names set to ['${secondary1.getName(defaultBusiness.getUUID())} (Name)', '${secondary2.getName(defaultBusiness.getUUID())} (Name)']"
        }

        displayActivities shouldContainOneItemWhere {
            it.title == "Other Emails set to ['${secondary1.email} (null)', '${secondary2.email} (null)']"
        }

        displayActivities shouldContainOneItemWhere {
            it.title == "Other Phone Numbers set to ['${secondary1.platformPhoneNumber} (null)', '${secondary2.platformPhoneNumber} (null)']"
        }

        listOf(secondary1, secondary2).forEach { user ->
            displayActivities shouldContainOneItemWhere { it.title == "Merged from user ${user.id}" }
        }
    }

    @Test
    fun `should create a user merge activity feed item for the secondary user`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id))

        listOf(secondary1, secondary2)
            .forEach { user ->
                val displayActivityGroup = activityService
                    .userDisplayActivity(user, null)
                    .first { it.name == "User merged" }

                val displayActivities = displayActivityGroup.activity!!
                displayActivities.size shouldBe 1
                displayActivities shouldContainOneItemWhere { it.title == "Merged to user ${primary.id}" }
            }
    }

    @Test
    fun `when merging ghost users together, ensure no ghost emails end up in the primary or other emails`() {
        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.nonNormalisedEmail = BusinessContactEmailUtils.makeGhostEmail(222L)
        }
        val user3 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.nonNormalisedEmail = BusinessContactEmailUtils.makeGhostEmail(333L)
            this.getBusinessContact(defaultBusiness.getUUID())?.otherEmails = listOf(BusinessContactFieldString(content = "<EMAIL>"))
        }
        val user4 = createUser(UserCreationParameters(4, listOf(defaultBusiness), ExternalUserType.Vendor, false)).save {
            this.nonNormalisedEmail = BusinessContactEmailUtils.makeGhostEmail(444L)
            this.getBusinessContact(defaultBusiness.getUUID())?.primaryEmail = "<EMAIL>"
            this.getBusinessContact(defaultBusiness.getUUID())?.otherEmails = listOf(BusinessContactFieldString(content = "<EMAIL>"))
        }

        mergeUserService.merge(
            request = MergeUserRequest(
                businessId = defaultBusiness.getUUID(),
                primaryUserId = user1.id,
                userIds = listOf(user1.id, user2.id, user3.id, user4.id),
                nameUserId = user1.id,
                phoneUserId = user1.id,
                addressUserId = user1.id, null, null, null
            ),
            dryRun = false,
            actor = salesAdminUser,
            mergeStrategy = ExternalCrmLinkMergeStrategy.MARK_LINK_AS_DELETED
        ).success()

        user1.email shouldBe "<EMAIL>"
        user1.getBusinessContact(defaultBusiness.getUUID())?.primaryEmail shouldBe null
        user1.getBusinessContact(defaultBusiness.getUUID())?.otherEmails shouldBe (listOf(BusinessContactFieldString(content = "<EMAIL>"),
            BusinessContactFieldString(content = "<EMAIL>"), BusinessContactFieldString(content = "<EMAIL>")))
    }

    @Test
    fun `when user merge is relaxed any user with not inactive properties can be either primary or secondary`() {
        setRelaxedUserMerge(true)

        val user1 = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Vendor, false))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        addProperty(user1, address(1)).save {
            this.campaignStatus = CampaignStatus.Sold
        }

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())

        val info = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        info.mustBePrimary shouldBe false
        info.mustBeSecondary shouldBe false
        info.primaryReasons.notInactivePropertyIds shouldBe null
    }

    @Test
    fun `when user merge is relaxed any user with no business contact can be either primary or secondary`() {
        setRelaxedUserMerge(true)

        val user1 = createUser(UserCreationParameters(1, listOf(), ExternalUserType.Vendor, false))
        val user2 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Vendor, false))

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(user1, user2), defaultBusiness.getUUID())
        val info = (infoMap[user1.id].success() as? UserMergeInformation.CanBeMerged).success()
        info.mustBePrimary shouldBe false
        info.mustBeSecondary shouldBe false
        info.primaryReasons.hasNoBusinessContact shouldBe false
    }

    private fun setRelaxedUserMerge(enabled: Boolean) {
        mockDefaultOrg {
            it.copy(
                featureConfiguration = LocalDev.featureConfiguration.copy(relaxedUserMerge = enabled)
            )
        }
    }

    @Test
    fun `should not merge users when secondary has no business contact`() {
        val userWithBusinessContact = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val userWithoutBusinessContact = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        businessContactRepository.get(userWithoutBusinessContact, defaultBusiness).success().save { deleted = true }

        businessContactRepository.get(userWithBusinessContact, defaultBusiness) shouldNotBe null
        businessContactRepository.get(userWithoutBusinessContact, defaultBusiness) shouldBe null

        val infoMap = mergeUserService.constructUserMergeInformation(
            users = listOf(userWithBusinessContact, userWithoutBusinessContact),
            businessId = defaultBusiness.getUUID()
        )
        val withBusinessContactUserInfo = (infoMap[userWithBusinessContact.id].success() as? UserMergeInformation.CanBeMerged).success()
        val withoutBusinessContactUserInfo = (infoMap[userWithoutBusinessContact.id].success() as? UserMergeInformation.CanBeMerged).success()

        withBusinessContactUserInfo.mustBePrimary shouldBe false
        withBusinessContactUserInfo.primaryReasons.hasNoBusinessContact shouldBe false

        withoutBusinessContactUserInfo.mustBePrimary shouldBe true
        withoutBusinessContactUserInfo.primaryReasons.hasNoBusinessContact shouldBe true

        val validMerge = mergeUserService.canMerge(
            constructMergeUserRequest(userWithoutBusinessContact, listOf(userWithBusinessContact, userWithoutBusinessContact)),
            false
        )
        validMerge.valid shouldBe true

        val invalidMerge = mergeUserService.canMerge(
            constructMergeUserRequest(userWithBusinessContact, listOf(userWithBusinessContact, userWithoutBusinessContact)),
            false
        )
        invalidMerge.valid shouldBe false
    }

    @Test
    fun `should not merge users when secondary is a platform user`() {
        val platformUser = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false, true))
        val nonPlatformUser = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false, false))

        platformUser.isPlatformUser shouldBe true
        nonPlatformUser.isPlatformUser shouldBe false

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(platformUser, nonPlatformUser), defaultBusiness.getUUID())
        val platformUserInfo = (infoMap[platformUser.id].success() as? UserMergeInformation.CanBeMerged).success()
        val nonPlatformUserInfo = (infoMap[nonPlatformUser.id].success() as? UserMergeInformation.CanBeMerged).success()

        platformUserInfo.mustBePrimary shouldBe true
        platformUserInfo.primaryReasons.isPlatformUser shouldBe true

        nonPlatformUserInfo.mustBePrimary shouldBe false
        nonPlatformUserInfo.primaryReasons.isPlatformUser shouldBe false

        val validMerge = mergeUserService.canMerge(
            constructMergeUserRequest(platformUser, listOf(platformUser, nonPlatformUser)),
            false
        )
        validMerge.valid shouldBe true

        val invalidMerge = mergeUserService.canMerge(
            constructMergeUserRequest(nonPlatformUser, listOf(platformUser, nonPlatformUser)),
            false
        )
        invalidMerge.valid shouldBe false
    }

    @Test
    fun `should merge users when secondary is a platform user if ignore is specified`() {
        val platformUser = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false, true))
        val nonPlatformUser = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false, false))

        platformUser.isPlatformUser shouldBe true
        nonPlatformUser.isPlatformUser shouldBe false

        val infoMap = mergeUserService.constructUserMergeInformation(listOf(platformUser, nonPlatformUser), defaultBusiness.getUUID())
        val platformUserInfo = (infoMap[platformUser.id].success() as? UserMergeInformation.CanBeMerged).success()
        val nonPlatformUserInfo = (infoMap[nonPlatformUser.id].success() as? UserMergeInformation.CanBeMerged).success()

        platformUserInfo.mustBePrimary shouldBe true
        platformUserInfo.primaryReasons.isPlatformUser shouldBe true

        nonPlatformUserInfo.mustBePrimary shouldBe false
        nonPlatformUserInfo.primaryReasons.isPlatformUser shouldBe false

        val validMerge = mergeUserService.canMerge(
            constructMergeUserRequest(platformUser, listOf(platformUser, nonPlatformUser)),
            true
        )
        validMerge.valid shouldBe true

        val invalidMerge = mergeUserService.canMerge(
            constructMergeUserRequest(nonPlatformUser, listOf(platformUser, nonPlatformUser)),
            ignoreNotPrimary = true
        )
        invalidMerge.valid shouldBe true
    }





    @Test
    fun `should not merge if primary user has more than 50 properties`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        (1..51).map { addProperty(primary, address(it)) }

        val mergeResult = defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).fail()

        mergeResult.valid shouldBe false
        mergeResult.problems?.size shouldBe 1
        mergeResult.problems?.get("tooManyProperties") shouldBe "true"
    }

    @Test
    fun `should not merge if primary user has more than 10 user merge activities`() {
        val primary = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.BuyerAndVendor, false))
        val secondary1 = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondary2 = createUser(UserCreationParameters(3, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        (1..11).forEach { _ -> activityRepository.put(UserMergedTo(defaultBusiness.id, primary.id).userIndex(Refs.ref(primary))) }

        val mergeResult = defaultMerge(primary.id, listOf(primary.id, secondary1.id, secondary2.id)).fail()

        mergeResult.valid shouldBe false
        mergeResult.problems?.size shouldBe 1
        mergeResult.problems?.get("tooManyUserMerges") shouldBe "true"
    }

    @Test
    fun `automerge should pick the name from the primary user if the secondary user name is not defined`() {
        // GIVEN
        val primaryUser = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondaryUser = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        secondaryUser.updateUserAndBusinessContact( { user -> user.setPlatformFirstName(""); user.setPlatformLastName("") }) { bc -> bc.crmFirstName = null; bc.crmLastName = null }

        // WHEN - users auto merged
        val mergeResult: MergeUserLog = mergeUserService.automaticMerge(primaryUser, secondaryUser, admin, defaultBusiness).success()

        // THEN - the name should be taken from the primary user
        mergeResult.nameUserId shouldBe primaryUser.id
        primaryUser.getFirstName(defaultBusiness.getUUID()) shouldBe primaryUser.getFirstName(defaultBusiness.getUUID())
        primaryUser.getLastName(defaultBusiness.getUUID()) shouldBe primaryUser.getLastName(defaultBusiness.getUUID())
    }

    @Test
    fun `automerge should pick the name from the secondary user if it's defined`() {
        // GIVEN
        val primaryUser = createUser(UserCreationParameters(1, listOf(defaultBusiness), ExternalUserType.Buyer, false))
        val secondaryUser = createUser(UserCreationParameters(2, listOf(defaultBusiness), ExternalUserType.Buyer, false))

        primaryUser.updateUserAndBusinessContact( { user -> user.setPlatformFirstName("Non"); user.setPlatformLastName("Empty") }) { bc -> bc.crmFirstName = "Non"; bc.crmLastName = "Empty" }
        secondaryUser.updateUserAndBusinessContact( { user -> user.setPlatformFirstName("Michael"); user.setPlatformLastName("Bolton") }) { bc -> bc.crmFirstName = "Michael"; bc.crmLastName = "Bolton" }

        // WHEN - users auto merged
        val mergeResult: MergeUserLog = mergeUserService.automaticMerge(primaryUser, secondaryUser, admin, defaultBusiness).success()

        // THEN - the name should be taken from the primary user
        mergeResult.nameUserId shouldBe secondaryUser.id
        primaryUser.getFirstName(defaultBusiness.getUUID()) shouldBe secondaryUser.getFirstName(defaultBusiness.getUUID())
        primaryUser.getLastName(defaultBusiness.getUUID()) shouldBe secondaryUser.getLastName(defaultBusiness.getUUID())
    }

    private fun User.updateUserAndBusinessContact(userUpdateFn: (User) -> Unit, businessContactUpdateFn: (BusinessContact) -> Unit): User {
        this.save {
            userUpdateFn(this)
        }
        businessContactRepository.get(this, defaultBusiness)!!
            .save {
                businessContactUpdateFn(this)
            }
        val user = this.reload()
        return user
    }

    private fun constructMergeUserRequest(
        primaryUser: User,
        users: List<User>
    ) = MergeUserRequest(
        businessId = defaultBusiness.getUUID(),
        primaryUserId = primaryUser.id,
        userIds = users.map { it.id },
        nameUserId = null,
        phoneUserId = null,
        addressUserId = null,
        dateOfBirthUserId = null,
        companyUserId = null,
        greetingUserId = null
    )
}
