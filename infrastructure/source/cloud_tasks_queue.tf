resource "google_cloud_tasks_queue" "agency_association_replication" {
  location = var.gcp_region
  name     = "agency-association-replication"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 80
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "1s"
  }
}

resource "google_cloud_tasks_queue" "amalgamations" {
  location = var.gcp_region
  name     = "amalgamations"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "amalgamations_activities" {
  location = var.gcp_region
  name     = "amalgamations-activities"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "amalgamations_contacts" {
  location = var.gcp_region
  name     = "amalgamations-contacts"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "amalgamations_cursor" {
  location = var.gcp_region
  name     = "amalgamations-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "amalgamations_interactions" {
  location = var.gcp_region
  name     = "amalgamations-interactions"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "amalgamations_postgres" {
  location = var.gcp_region
  name     = "amalgamations-postgres"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "amalgamations_teams" {
  location = var.gcp_region
  name     = "amalgamations-teams"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "appointment_attendee_move" {
  location = var.gcp_region
  name     = "appointment-attendee-move"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "arc_2448_single" {
  location = var.gcp_region
  name     = "arc-2448-single"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "arc_2448_traverse_single" {
  location = var.gcp_region
  name     = "arc-2448-traverse-single"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "arc_5233_migration_batch" {
  location = var.gcp_region
  name     = "arc-5233-migration-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "arc_5233_migration_traverse" {
  location = var.gcp_region
  name     = "arc-5233-migration-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "arc_5905_migration_for_reads" {
  location = var.gcp_region
  name     = "arc-5905-migration-for-reads"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "arc_5905_migration_for_writes" {
  location = var.gcp_region
  name     = "arc-5905-migration-for-writes"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "autopilot" {
  location = var.gcp_region
  name     = "autopilot"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "259200s"
    min_backoff        = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "backup" {
  location = var.gcp_region
  name     = "backup"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = 1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "bootstrap" {
  location = var.gcp_region
  name     = "bootstrap"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 15
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "604800s"
    min_backoff        = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "buyer_requirement" {
  location = var.gcp_region
  name     = "buyer-requirement"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "buyer_requirement_dead_letter" {
  location = var.gcp_region
  name     = "buyer-requirement-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "calendar" {
  location = var.gcp_region
  name     = "calendar"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 75
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "check_in_appointment_attendee_datastore_task" {
  location = var.gcp_region
  name     = "check-in-appointment-attendee-datastore-task"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "check_in_appointment_attendee_from_vault_note" {
  location = var.gcp_region
  name     = "check-in-appointment-attendee-from-vault-note"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "checkpointing_test_batch" {
  location = var.gcp_region
  name     = "checkpointing-test-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "checkpointing_test_single" {
  location = var.gcp_region
  name     = "checkpointing-test-single"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "checkpointing_test_traverse" {
  location = var.gcp_region
  name     = "checkpointing-test-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "cleanup" {
  location = var.gcp_region
  name     = "cleanup"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "confirmed_appointment_attendee_datastore_task" {
  location = var.gcp_region
  name     = "confirmed-appointment-attendee-datastore-task"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "contract_of_sale_request_created" {
  location = var.gcp_region
  name     = "contract-of-sale-request-created"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "core_logic_pull" {
  location = var.gcp_region
  name     = "core-logic-pull"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 0.066666667
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "create_contract_of_sale_request" {
  location = var.gcp_region
  name     = "create-contract-of-sale-request"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "create_interaction" {
  location = var.gcp_region
  name     = "create-interaction"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "dapi_publish_in_background" {
  location = var.gcp_region
  name     = "dapi-publish-in-background"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "data_api_datastore_domain_component_migration_batch" {
  location = var.gcp_region
  name     = "data-api-datastore-domain-component-migration-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 40
    max_dispatches_per_second = 40
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "data_api_datastore_domain_component_migration_cursor" {
  location = var.gcp_region
  name     = "data-api-datastore-domain-component-migration-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 150
    max_dispatches_per_second = 150
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "300s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

resource "google_cloud_tasks_queue" "data_api_migration_batch" {
  location = var.gcp_region
  name     = "data-api-migration-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 150
    max_dispatches_per_second = 150
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "data_api_postgres_domain_component_migration_initiator" {
  location = var.gcp_region
  name     = "data-api-postgres-domain-component-migration-initiator"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "data_api_postgres_domain_component_migration_job_executor" {
  location = var.gcp_region
  name     = "data-api-postgres-domain-component-migration-job-executor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 25
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "data_api_postgres_domain_component_migration_job_injector" {
  location = var.gcp_region
  name     = "data-api-postgres-domain-component-migration-job-injector"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "data_api_postgres_domain_component_migration_job_migrate_entity" {
  location = var.gcp_region
  name     = "data-api-postgres-domain-component-migration-job-migrate-entity"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 40
    max_dispatches_per_second = 40
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "data_sync" {
  location = var.gcp_region
  name     = "data-sync"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "de_amalgamations_activities" {
  location = var.gcp_region
  name     = "de-amalgamations-activities"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "de_amalgamations_contacts" {
  location = var.gcp_region
  name     = "de-amalgamations-contacts"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "de_amalgamations_cursor" {
  location = var.gcp_region
  name     = "de-amalgamations-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "de_amalgamations_interactions" {
  location = var.gcp_region
  name     = "de-amalgamations-interactions"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "de_amalgamations_postgres" {
  location = var.gcp_region
  name     = "de-amalgamations-postgres"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "de_amalgamations_properties" {
  location = var.gcp_region
  name     = "de-amalgamations-properties"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "de_amalgamations_teams" {
  location = var.gcp_region
  name     = "de-amalgamations-teams"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "default" {
  location = var.gcp_region
  name     = "default"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "dis_1766_migration_batch" {
  location = var.gcp_region
  name     = "dis-1766-migration-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "dis_1766_migration_traverse" {
  location = var.gcp_region
  name     = "dis-1766-migration-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "domain" {
  location = var.gcp_region
  name     = "domain"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "domain_enquiries_and_performance" {
  location = var.gcp_region
  name     = "domain-enquiries-and-performance"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

resource "google_cloud_tasks_queue" "domain_lead_miner" {
  location = var.gcp_region
  name     = "domain-lead-miner"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "86400s"
    min_backoff        = "300s"
  }
}

resource "google_cloud_tasks_queue" "domain_oauth" {
  location = var.gcp_region
  name     = "domain-oauth"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

resource "google_cloud_tasks_queue" "etl" {
  location = var.gcp_region
  name     = "etl"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts       = 31
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "10800s"
    min_backoff        = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "etl_export" {
  location = var.gcp_region
  name     = "etl-export"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "10s"
  }
}

resource "google_cloud_tasks_queue" "etl_import" {
  location = var.gcp_region
  name     = "etl-import"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "etl_mv" {
  location = var.gcp_region
  name     = "etl-mv"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts       = 31
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "10800s"
    min_backoff        = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "etl_spark_restream" {
  location = var.gcp_region
  name     = "etl-spark-restream"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "etl_stream" {
  location = var.gcp_region
  name     = "etl-stream"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "10s"
  }
}

resource "google_cloud_tasks_queue" "etl_stream_activity_group" {
  location = var.gcp_region
  name     = "etl-stream-activity-group"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 8
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "10s"
  }
}

resource "google_cloud_tasks_queue" "etl_stream_external_event" {
  location = var.gcp_region
  name     = "etl-stream-external-event"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 300
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "10s"
  }
}

resource "google_cloud_tasks_queue" "etl_stream_incoming" {
  location = var.gcp_region
  name     = "etl-stream-incoming"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "900s"
    min_backoff        = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "etl_stream_matched_leads" {
  location = var.gcp_region
  name     = "etl-stream-matched-leads"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "10s"
  }
}

resource "google_cloud_tasks_queue" "etl_stream_sparks" {
  location = var.gcp_region
  name     = "etl-stream-sparks"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 500
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "15s"
  }
}

resource "google_cloud_tasks_queue" "etl_stream_user_action" {
  location = var.gcp_region
  name     = "etl-stream-user-action"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "1209600s"
    min_backoff        = "10s"
  }
}

resource "google_cloud_tasks_queue" "external_event" {
  location = var.gcp_region
  name     = "external-event"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "300s"
  }
}

resource "google_cloud_tasks_queue" "external_event_on_accept" {
  location = var.gcp_region
  name     = "external-event-on-accept"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "300s"
  }
}

resource "google_cloud_tasks_queue" "fanout" {
  location = var.gcp_region
  name     = "fanout"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 500
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "1s"
  }
}

resource "google_cloud_tasks_queue" "fetch_property_details" {
  location = var.gcp_region
  name     = "fetch-property-details"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 25
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "fix_context_meeting_tfer" {
  location = var.gcp_region
  name     = "fix-context-meeting-tfer"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 15
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "fix_context_reminder_tfer" {
  location = var.gcp_region
  name     = "fix-context-reminder-tfer"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 15
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "import_extracted_notes_dead_letter" {
  location = var.gcp_region
  name     = "import-extracted-notes-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "import_properties_photos" {
  location = var.gcp_region
  name     = "import-properties-photos"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "120s"
  }
}

resource "google_cloud_tasks_queue" "maps" {
  location = var.gcp_region
  name     = "maps"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "matched_leads" {
  location = var.gcp_region
  name     = "matched-leads"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 150
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "86400s"
    min_backoff        = "60s"
  }
}

resource "google_cloud_tasks_queue" "merge_event" {
  location = var.gcp_region
  name     = "merge-event"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

resource "google_cloud_tasks_queue" "merge_user" {
  location = var.gcp_region
  name     = "merge-user"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "merge_user_dead_letter" {
  location = var.gcp_region
  name     = "merge-user-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "merge_user_update_contact_for_notes" {
  location = var.gcp_region
  name     = "merge-user-update-contact-for-notes"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_2131_batch" {
  location = var.gcp_region
  name     = "migrate-api-2131-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_2131_cursor" {
  location = var.gcp_region
  name     = "migrate-api-2131-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_2131_dead_letter" {
  location = var.gcp_region
  name     = "migrate-api-2131-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_2722_batch" {
  location = var.gcp_region
  name     = "migrate-api-2722-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_2722_cursor" {
  location = var.gcp_region
  name     = "migrate-api-2722-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_3387_batch" {
  location = var.gcp_region
  name     = "migrate-api-3387-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_3387_cursor" {
  location = var.gcp_region
  name     = "migrate-api-3387-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_3550_batch" {
  location = var.gcp_region
  name     = "migrate-api-3550-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_3550_cursor" {
  location = var.gcp_region
  name     = "migrate-api-3550-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_3797_batch" {
  location = var.gcp_region
  name     = "migrate-api-3797-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_3797_cursor" {
  location = var.gcp_region
  name     = "migrate-api-3797-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_4108" {
  location = var.gcp_region
  name     = "migrate-api-4108"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_4127" {
  location = var.gcp_region
  name     = "migrate-api-4127"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_4205" {
  location = var.gcp_region
  name     = "migrate-api-4205"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_4234" {
  location = var.gcp_region
  name     = "migrate-api-4234"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_4300" {
  location = var.gcp_region
  name     = "migrate-api-4300"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_api_4304" {
  location = var.gcp_region
  name     = "migrate-api-4304"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_1491" {
  location = var.gcp_region
  name     = "migrate-arc-1491"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 3
    max_dispatches_per_second = 3
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_1491_cursor" {
  location = var.gcp_region
  name     = "migrate-arc-1491-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2084_batch" {
  location = var.gcp_region
  name     = "migrate-arc-2084-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2084_traverse" {
  location = var.gcp_region
  name     = "migrate-arc-2084-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2543_resolve" {
  location = var.gcp_region
  name     = "migrate-arc-2543-resolve"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2543_traverse" {
  location = var.gcp_region
  name     = "migrate-arc-2543-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2620" {
  location = var.gcp_region
  name     = "migrate-arc-2620"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2621" {
  location = var.gcp_region
  name     = "migrate-arc-2621"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2638" {
  location = var.gcp_region
  name     = "migrate-arc-2638"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_2638_dapi_publish_in_background" {
  location = var.gcp_region
  name     = "migrate-arc-2638-dapi-publish-in-background"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_3159" {
  location = var.gcp_region
  name     = "migrate-arc-3159"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 3
    max_dispatches_per_second = 3
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_3159_cursor" {
  location = var.gcp_region
  name     = "migrate-arc-3159-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_3746" {
  location = var.gcp_region
  name     = "migrate-arc-3746"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_4652_cursor" {
  location = var.gcp_region
  name     = "migrate-arc-4652-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_4672" {
  location = var.gcp_region
  name     = "migrate-arc-4672"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_6790" {
  location = var.gcp_region
  name     = "migrate-arc-6790"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_8285" {
  location = var.gcp_region
  name     = "migrate-arc-8285"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_6798" {
  location = var.gcp_region
  name     = "arc-6798-migration-update-ml-id-on-activity"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate-arc-8016" {
  location = var.gcp_region
  name     = "migrate-arc-8016"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 3
    max_dispatches_per_second = 3
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_basic_suburb_batch" {
  location = var.gcp_region
  name     = "migrate-basic-suburb-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_basic_suburb_cursor" {
  location = var.gcp_region
  name     = "migrate-basic-suburb-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_basic_suburb_trigger" {
  location = var.gcp_region
  name     = "migrate-basic-suburb-trigger"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_buyer_requirements_to_dapi" {
  location = var.gcp_region
  name     = "migrate-buyer-requirements-to-dapi"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_1130_resolve" {
  location = var.gcp_region
  name     = "migrate-dis-1130-resolve"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_1130_traverse" {
  location = var.gcp_region
  name     = "migrate-dis-1130-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_1439" {
  location = var.gcp_region
  name     = "migrate-dis-1439"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_378" {
  location = var.gcp_region
  name     = "migrate-dis-378"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_687_remove_appointment_attendees" {
  location = var.gcp_region
  name     = "migrate-dis-687-remove-appointment-attendees"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_687_remove_appointments" {
  location = var.gcp_region
  name     = "migrate-dis-687-remove-appointments"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_687_remove_interested_buyers" {
  location = var.gcp_region
  name     = "migrate-dis-687-remove-interested-buyers"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_687_traverse" {
  location = var.gcp_region
  name     = "migrate-dis-687-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_dis_957" {
  location = var.gcp_region
  name     = "migrate-dis-957"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_external_property_data_batch" {
  location = var.gcp_region
  name     = "migrate-external-property-data-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 15
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "migrate_int_147" {
  location = var.gcp_region
  name     = "migrate-int-147"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_int_158" {
  location = var.gcp_region
  name     = "migrate-int-158"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_nc_6206" {
  location = var.gcp_region
  name     = "migrate-nc-6206"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_2" {
  location = var.gcp_region
  name     = "migration-2"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "300s"
  }
}

resource "google_cloud_tasks_queue" "delete_entity_records" {
  location = var.gcp_region
  name     = "delete-entity-records"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "300s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_1900_batch" {
  location = var.gcp_region
  name     = "migration-api-1900-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_1900_cursor" {
  location = var.gcp_region
  name     = "migration-api-1900-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_1900_dead_letter" {
  location = var.gcp_region
  name     = "migration-api-1900-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "86400s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_2110_batch" {
  location = var.gcp_region
  name     = "migration-api-2110-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_2110_cursor" {
  location = var.gcp_region
  name     = "migration-api-2110-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_2737_batch" {
  location = var.gcp_region
  name     = "migration-api-2737-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_2737_cursor" {
  location = var.gcp_region
  name     = "migration-api-2737-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_2966_batch" {
  location = var.gcp_region
  name     = "migration-api-2966-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_2966_cursor" {
  location = var.gcp_region
  name     = "migration-api-2966-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_3143_batch" {
  location = var.gcp_region
  name     = "migration-api-3143-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_3143_cursor" {
  location = var.gcp_region
  name     = "migration-api-3143-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api_3143_dlq" {
  location = var.gcp_region
  name     = "migration-api-3143-dlq"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api1935_batch" {
  location = var.gcp_region
  name     = "migration-api1935-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_api1935_cursor" {
  location = var.gcp_region
  name     = "migration-api1935-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_arc_4376_cleanup_rw_chelsea_batch" {
  location = var.gcp_region
  name     = "migration-arc-4376-cleanup-rw-chelsea-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 2
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_arc_4376_cleanup_rw_chelsea_traverse" {
  location = var.gcp_region
  name     = "migration-arc-4376-cleanup-rw-chelsea-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_arc_4376_vault_link_batch" {
  location = var.gcp_region
  name     = "migration-arc-4376-vault-link-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 2
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_arc_4376_vault_link_traverse" {
  location = var.gcp_region
  name     = "migration-arc-4376-vault-link-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_arc_6598_fix_portals_batch" {
  location = var.gcp_region
  name     = "migration-arc-6598-fix-portals-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 2
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_arc_6598_fix_portals_traverse" {
  location = var.gcp_region
  name     = "migration-arc-6598-fix-portals-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migration_resolve_broken_persona_refs" {
  location = var.gcp_region
  name     = "migration-resolve-broken-persona-refs"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "nc_5547_migration_batch" {
  location = var.gcp_region
  name     = "nc-5547-migration-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "nc_5547_migration_traverse" {
  location = var.gcp_region
  name     = "nc-5547-migration-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "notification_message_events" {
  location = var.gcp_region
  name     = "notification-message-events"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "notifications" {
  location = var.gcp_region
  name     = "notifications"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "postgres_domain_component_outbox_flusher" {
  location = var.gcp_region
  name     = "postgres-domain-component-outbox-flusher"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "prerender" {
  location = var.gcp_region
  name     = "prerender"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 2
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "prerenderUser" {
  location = var.gcp_region
  name     = "prerenderUser"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "property_attendee_ds_queue" {
  location = var.gcp_region
  name     = "property-attendee-ds-queue"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 40
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "property_classification_migration" {
  location = var.gcp_region
  name     = "property-classification-migration"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "property_permissions" {
  location = var.gcp_region
  name     = "property-permissions"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "delete_revoked_property_permissions" {
  location = var.gcp_region
  name     = "revoke-property-permission-grants"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "property_search_notification" {
  location = var.gcp_region
  name     = "property-search-notification"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = 1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "property_search_notification_dead_letter" {
  location = var.gcp_region
  name     = "property-search-notification-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "push_notifications" {
  location = var.gcp_region
  name     = "push-notifications"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = 1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "ray_white_sync" {
  location = var.gcp_region
  name     = "ray-white-sync"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = 1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "ray_white_sync_deactivate_users" {
  location = var.gcp_region
  name     = "ray-white-sync-deactivate-users"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "86400s"
    min_backoff        = "3s"
  }
}

resource "google_cloud_tasks_queue" "ray_white_sync_listings" {
  location = var.gcp_region
  name     = "ray-white-sync-listings"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "86400s"
    min_backoff        = "1s"
  }
}

resource "google_cloud_tasks_queue" "ray_white_sync_users" {
  location = var.gcp_region
  name     = "ray-white-sync-users"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 80
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "86400s"
    min_backoff        = "1s"
  }
}

resource "google_cloud_tasks_queue" "rea" {
  location = var.gcp_region
  name     = "rea"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "rea_xml" {
  location = var.gcp_region
  name     = "rea-xml"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "refresh_user_team_roles" {
  location = var.gcp_region
  name     = "refresh-user-team-roles"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "remove_user_from_team" {
  location = var.gcp_region
  name     = "remove-user-from-team"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "reorg_appraisals_nz" {
  location = var.gcp_region
  name     = "reorg-appraisals-nz"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "retry_sending_notifications" {
  location = var.gcp_region
  name     = "retry-sending-notifications"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "search_replication" {
  location = var.gcp_region
  name     = "search-replication"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 40
    max_dispatches_per_second = 80
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

# Used monthly to make sure data is in sync
resource "google_cloud_tasks_queue" "search_replication_traverser" {
  location = var.gcp_region
  name     = "search-replication-traverser"
  project  = var.gcp_project_id

  rate_limits {
    # no more than one cursor task running at once
    max_concurrent_dispatches = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

resource "google_cloud_tasks_queue" "self_check_in_appointment_attendee" {
  location = var.gcp_region
  name     = "self-check-in-appointment-attendee"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "send_appraisal_sms_notification" {
  location = var.gcp_region
  name     = "send-appraisal-sms-notification"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "send_bulk_notification" {
  location = var.gcp_region
  name     = "send-bulk-notification"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "send_notification_chunk" {
  location = var.gcp_region
  name     = "send-notification-chunk"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 2
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "send_ofi_welcome_sms_notification" {
  location = var.gcp_region
  name     = "send-ofi-welcome-sms-notification"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "172800s"
    min_backoff        = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "spark_deactivate" {
  location = var.gcp_region
  name     = "spark-deactivate"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "spark_dismiss" {
  location = var.gcp_region
  name     = "spark-dismiss"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "spark_generate" {
  location = var.gcp_region
  name     = "spark-generate"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 500
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "3600s"
    max_doublings      = 16
    max_retry_duration = "86400s"
    min_backoff        = "10s"
  }
}

resource "google_cloud_tasks_queue" "suburb" {
  location = var.gcp_region
  name     = "suburb"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "suburb_cache_au" {
  location = var.gcp_region
  name     = "suburb-cache-au"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 9
    max_dispatches_per_second = 9
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "suburb_cache_nz" {
  location = var.gcp_region
  name     = "suburb-cache-nz"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "system" {
  location = var.gcp_region
  name     = "system"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "team_events" {
  location = var.gcp_region
  name     = "team-events"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 50
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "team_replication" {
  location = var.gcp_region
  name     = "team-replication"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "team_user_permission_updates" {
  location = var.gcp_region
  name     = "team-user-permission-updates"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "token" {
  location = var.gcp_region
  name     = "token"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "trigger_buyer_requirements_migration_to_dapi" {
  location = var.gcp_region
  name     = "trigger-buyer-requirements-migration-to-dapi"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "unpostpone_sparks_by_agent_and_vendor" {
  location = var.gcp_region
  name     = "unpostpone-sparks-by-agent-and-vendor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "update_property_owners" {
  location = var.gcp_region
  name     = "update-property-owners"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "update_property_owners_dead_letter" {
  location = var.gcp_region
  name     = "update-property-owners-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "user_search_replication_job" {
  location = var.gcp_region
  name     = "user-search-replication-job"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "user_search_replication_job_cursor" {
  location = var.gcp_region
  name     = "user-search-replication-job-cursor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_bulk_import_contact_notes" {
  location = var.gcp_region
  name     = "vault-bulk-import-contact-notes"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 6
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

resource "google_cloud_tasks_queue" "vault_bulk_import_contact_notes_decompress" {
  location = var.gcp_region
  name     = "vault-bulk-import-contact-notes-decompress"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "21600s"
    max_doublings = 16
    min_backoff   = "300s"
  }
}

resource "google_cloud_tasks_queue" "vault_bulk_import_contact_notes_decompress_dead_letter" {
  location = var.gcp_region
  name     = "vault-bulk-import-contact-notes-decompress-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_bulk_import_contact_notes_iterate" {
  location = var.gcp_region
  name     = "vault-bulk-import-contact-notes-iterate"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 50
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "180s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_bulk_import_contact_notes_trigger_export" {
  location = var.gcp_region
  name     = "vault-bulk-import-contact-notes-trigger-export"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 0.016666667
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "900s"
  }
}

resource "google_cloud_tasks_queue" "vault_bulk_import_contact_notes_trigger_export_dead_letter" {
  location = var.gcp_region
  name     = "vault-bulk-import-contact-notes-trigger-export-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_bulk_import_processed_enquiry" {
  location = var.gcp_region
  name     = "vault-bulk-import-processed-enquiry"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 6
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "120s"
    max_doublings = 16
    min_backoff   = "10s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_confirmed_appointment_attendee_property_feedback_note" {
  location = var.gcp_region
  name     = "vault-export-confirmed-appointment-attendee-property-feedback-note"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact" {
  location = var.gcp_region
  name     = "vault-export-contact"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact_dead_letter" {
  location = var.gcp_region
  name     = "vault-export-contact-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 0
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "604800s"
    max_doublings = 16
    min_backoff   = "86400s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact_notes" {
  location = var.gcp_region
  name     = "vault-export-contact-notes"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact_notes_dead_letter" {
  location = var.gcp_region
  name     = "vault-export-contact-notes-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "900s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact_properties" {
  location = var.gcp_region
  name     = "vault-export-contact-properties"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact_properties_dead_letter" {
  location = var.gcp_region
  name     = "vault-export-contact-properties-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact_properties_owners" {
  location = var.gcp_region
  name     = "vault-export-contact-properties-owners"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_contact_properties_owners_dead_letter" {
  location = var.gcp_region
  name     = "vault-export-contact-properties-owners-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_export_open_home" {
  location = var.gcp_region
  name     = "vault-export-open-home"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_account_token" {
  location = var.gcp_region
  name     = "vault-import-account-token"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_account_users" {
  location = var.gcp_region
  name     = "vault-import-account-users"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault-import-agency-everything" {
  location = var.gcp_region
  name     = "vault-import-agency-everything"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 60
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts = -1
    max_backoff = "86400s"
    max_doublings = 16
    min_backoff = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault-import-agency-everything-dead-letter" {
  location = var.gcp_region
  name     = "vault-import-agency-everything-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 0
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "604800s"
    max_doublings = 16
    min_backoff   = "86400s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_calendar_event" {
  location = var.gcp_region
  name     = "vault-import-calendar-event"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_calendar_event_dead_letter" {
  location = var.gcp_region
  name     = "vault-import-calendar-event-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact" {
  location = var.gcp_region
  name     = "vault-import-contact"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 60
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_dead_letter" {
  location = var.gcp_region
  name     = "vault-import-contact-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 0
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "604800s"
    max_doublings = 16
    min_backoff   = "86400s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_notes" {
  location = var.gcp_region
  name     = "vault-import-contact-notes"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 30
    max_dispatches_per_second = 20
  }

  retry_config {
    max_attempts       = -1
    max_backoff        = "86400s"
    max_doublings      = 16
    max_retry_duration = "86400s"
    min_backoff        = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_properties" {
  location = var.gcp_region
  name     = "vault-import-contact-properties"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 30
    max_dispatches_per_second = 15
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_properties_dead_letter" {
  location = var.gcp_region
  name     = "vault-import-contact-properties-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 0
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "604800s"
    max_doublings = 16
    min_backoff   = "86400s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_properties_features" {
  location = var.gcp_region
  name     = "vault-import-contact-properties-features"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 30
    max_dispatches_per_second = 15
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_properties_offere" {
  location = var.gcp_region
  name     = "vault-import-contact-properties-offere"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_properties_offers" {
  location = var.gcp_region
  name     = "vault-import-contact-properties-offers"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 10
    max_dispatches_per_second = 5
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "3600s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_contact_properties_previous_lifecycles" {
  location = var.gcp_region
  name     = "vault-import-contact-properties-previous-lifecycles"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 40
    max_dispatches_per_second = 15
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_processed_enquiry" {
  location = var.gcp_region
  name     = "vault-import-processed-enquiry"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 4
    max_dispatches_per_second = 2
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_processed_enquiry_dead_letter" {
  location = var.gcp_region
  name     = "vault-import-processed-enquiry-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_processed_enquiry_real_time" {
  location = var.gcp_region
  name     = "vault-import-processed-enquiry-real-time"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 60
    max_dispatches_per_second = 30
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_processed_enquiry_real_time_dead_letter" {
  location = var.gcp_region
  name     = "vault-import-processed-enquiry-real-time-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_import_user_ids" {
  location = var.gcp_region
  name     = "vault-import-user-ids"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_merge_contact" {
  location = var.gcp_region
  name     = "vault-merge-contact"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_merge_contact_dead_letter" {
  location = var.gcp_region
  name     = "vault-merge-contact-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_merge_property" {
  location = var.gcp_region
  name     = "vault-merge-property"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 1
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "vault_merge_property_dead_letter" {
  location = var.gcp_region
  name     = "vault-merge-property-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_migrate" {
  location = var.gcp_region
  name     = "vault-migrate"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_note_sideeffect" {
  location = var.gcp_region
  name     = "vault-note-sideeffect"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "30s"
  }
}

resource "google_cloud_tasks_queue" "vault_note_sideeffect_dead_letter" {
  location = var.gcp_region
  name     = "vault-note-sideeffect-dead-letter"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 0
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "vault_webhook_processor" {
  location = var.gcp_region
  name     = "vault-webhook-processor"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 200
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "1s"
  }
}

resource "google_cloud_tasks_queue" "weekly_email" {
  location = var.gcp_region
  name     = "weekly-email"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 200
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "86400s"
    max_doublings = 16
    min_backoff   = "60s"
  }
}

resource "google_cloud_tasks_queue" "weekly_reporting" {
  location = var.gcp_region
  name     = "weekly-reporting"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "weekly_reporting_email" {
  location = var.gcp_region
  name     = "weekly-reporting-email"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "weekly_reporting_generate" {
  location = var.gcp_region
  name     = "weekly-reporting-generate"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "sms_unsubscribe_migration_batch" {
  location = var.gcp_region
  name     = "sms-unsubscribe-migration-batch"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 300
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "sms_unsubscribe_migration_traverse" {
  location = var.gcp_region
  name     = "sms-unsubscribe-migration-traverse"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "1s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_7750" {
  location = var.gcp_region
  name     = "migrate-arc-7750"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "acr_grant" {
  location = var.gcp_region
  name     = "acr-grant-or-revoke"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 20
    max_dispatches_per_second = 500
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "migrate_arc_6973" {
  location = var.gcp_region
  name     = "migrate-arc-6973"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 100
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "acknowledge_hazard_and_risk_activity_task" {
  location = var.gcp_region
  name     = "acknowledge-hazard-and-risk-activity-task"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 1000
    max_dispatches_per_second = 100
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "0.100s"
  }
}

resource "google_cloud_tasks_queue" "ex_568_migration_for_notification_status_org_name" {
  location = var.gcp_region
  name     = "ex-568-migration-for-notification-status-org-name"
  project  = var.gcp_project_id

  rate_limits {
    max_concurrent_dispatches = 5
    max_dispatches_per_second = 10
  }

  retry_config {
    max_attempts  = -1
    max_backoff   = "3600s"
    max_doublings = 16
    min_backoff   = "1s"
  }
}
